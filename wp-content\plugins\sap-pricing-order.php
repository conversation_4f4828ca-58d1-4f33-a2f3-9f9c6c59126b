<?php
/**
 * Plugin Name: SAP Product Pricing (Single Product)
 * Description: Calls external SAP SalesOrderS endpoint on single product pages and displays Net Price and Sales Tax.
 * Version: 0.1.0
 * Author: Augment Code
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
// Global sanity log to verify plugin is active (runs on all pages)
add_action( 'wp_head', function() {
    // Only log once per request
    static $done = false; if ( $done ) return; $done = true;
    $has_url   = (bool) hytec_sap_api_get_url();
    $has_token = (bool) hytec_sap_api_get_auth_header();
    $has_cust  = is_user_logged_in() ? (bool) hytec_get_sap_customer_for_user( get_current_user_id() ) : false;
    hytec_sap_console_log('log', 'SAP Product Pricing plugin active', array(
        'is_product'      => is_product(),
        'user_logged_in'  => is_user_logged_in(),
        'has_customer'    => $has_cust,
        'has_url'         => $has_url,
        'has_token'       => $has_token,
    ));
});


// Simple console logging helper (avoids leaking secrets)
function hytec_sap_pricing_console_enabled() {
    $enabled = true; // default on for rollout; can be disabled via constant or filter
    if ( defined( 'HYTEC_SAP_PRICING_CONSOLE' ) ) {
        $enabled = (bool) HYTEC_SAP_PRICING_CONSOLE;
    }
    return (bool) apply_filters( 'hytec_sap_pricing_console_enabled', $enabled );
}

function hytec_sap_debug_ui_enabled() {
    $enabled = true; // default on for rollout, disable via constant or filter in production
    if ( defined( 'HYTEC_SAP_DEBUG_UI' ) ) {
        $enabled = (bool) HYTEC_SAP_DEBUG_UI;
    }
    return (bool) apply_filters( 'hytec_sap_debug_ui_enabled', $enabled );
}

function hytec_render_pricing_section( $net_value, $sales_tax, $note = '' ) {
    echo '<section class="sap-pricing" style="margin-top:15px;">';
    echo '<h3 class="sap-pricing-title" style="font-size:1rem;margin-bottom:8px;">Pricing</h3>';
    echo '<table class="shop_attributes" role="presentation" style="width:auto;">';
    echo '<tbody>';
    echo '<tr><th>Net Price</th><td>' . esc_html( (string) $net_value ) . '</td></tr>';
    echo '<tr><th>Sales Tax</th><td>' . esc_html( (string) $sales_tax ) . '</td></tr>';
    echo '</tbody>';
    echo '</table>';
    if ( $note !== '' && hytec_sap_debug_ui_enabled() ) {
        echo '<div class="sap-pricing-note" style="margin-top:6px;color:#888;font-size:12px;">' . esc_html( $note ) . '</div>';
    }
    echo '</section>';
}

function hytec_sap_console_log( $level, $message, $context = array() ) {
    if ( ! hytec_sap_pricing_console_enabled() ) return;
    if ( ! in_array( $level, array( 'log', 'info', 'warn', 'error' ), true ) ) {
        $level = 'log';
    }
    // Remove sensitive data if accidentally passed
    foreach ( array( 'Authorization','authorization','token','auth' ) as $k ) {
        if ( isset( $context[ $k ] ) ) unset( $context[ $k ] );
    }
    $data = array( 'msg' => (string) $message, 'ctx' => $context );
    $json = wp_json_encode( $data );
    //echo "<script>(function(){try{console[$level]($json);}catch(e){}})();</script>";
}

// Store and print last API request for debugging (no secrets)
function hytec_sap_set_last_request_debug( $data ) {
    $GLOBALS['HYTEC_SAP_LAST_REQUEST'] = $data;
}
function hytec_sap_get_last_request_debug() {
    return isset( $GLOBALS['HYTEC_SAP_LAST_REQUEST'] ) ? $GLOBALS['HYTEC_SAP_LAST_REQUEST'] : null;
}
function hytec_sap_print_request_debug() {
    if ( ! hytec_sap_debug_ui_enabled() ) return;
    $req = hytec_sap_get_last_request_debug();
    if ( ! $req ) return;
    // Render a collapsible block with method, url, headers, and body
    echo '<details class="sap-request-debug" style="margin-top:8px;">';
    echo '<summary>SAP API Request (debug)</summary>';
    echo '<div style="font-family:monospace;font-size:12px;line-height:1.4;">';
    echo '<div><strong>Method:</strong> ' . esc_html( isset($req['method']) ? $req['method'] : 'POST' ) . '</div>';
    echo '<div><strong>URL:</strong> ' . esc_html( isset($req['url']) ? $req['url'] : '' ) . '</div>';
    if ( ! empty( $req['headers'] ) && is_array( $req['headers'] ) ) {
        echo '<div><strong>Headers:</strong></div><pre style="white-space:pre-wrap;max-height:240px;overflow:auto;">' . esc_html( print_r( $req['headers'], true ) ) . '</pre>';
    }
    if ( isset( $req['body'] ) ) {
        echo '<div><strong>Body:</strong></div><pre style="white-space:pre-wrap;max-height:400px;overflow:auto;">' . esc_html( (string) $req['body'] ) . '</pre>';
    }
    echo '</div></details>';
}


// File logging helpers
function hytec_sap_log_dir() {
    return WP_CONTENT_DIR . '/logs';
}
function hytec_sap_log_path() {
    return hytec_sap_log_dir() . '/SAPPrice.log';
}
function hytec_sap_ensure_log_dir() {
    $dir = hytec_sap_log_dir();
    if ( ! file_exists( $dir ) ) {
        wp_mkdir_p( $dir );
    }
}
function hytec_sap_write_log( $label, $data = array() ) {
    hytec_sap_ensure_log_dir();
    $entry = array(
        'ts'    => date_i18n( 'Y-m-d H:i:s' ),
        'label' => (string) $label,
        'url'   => isset($_SERVER['REQUEST_URI']) ? (string) $_SERVER['REQUEST_URI'] : '',
        'user'  => is_user_logged_in() ? get_current_user_id() : 0,
        'data'  => $data,
    );
    $line = wp_json_encode( $entry ) . PHP_EOL;
    @file_put_contents( hytec_sap_log_path(), $line, FILE_APPEND | LOCK_EX );
}
function hytec_sap_sanitize_headers_for_log( $headers ) {
    if ( ! is_array( $headers ) ) return $headers;
    if ( isset( $headers['Authorization'] ) ) {
        $headers['Authorization'] = 'Bearer ***redacted***';
    }
    return $headers;
}


// Allow configuration via environment variables or PHP constants.

// Per-request pricing store
function hytec_sap_set_last_pricing( $product_id, $data ) {
    if ( ! isset( $GLOBALS['HYTEC_SAP_PRICING'] ) ) $GLOBALS['HYTEC_SAP_PRICING'] = array();
    $GLOBALS['HYTEC_SAP_PRICING'][ intval($product_id) ] = $data;
}
function hytec_sap_get_last_pricing( $product_id ) {
    return isset( $GLOBALS['HYTEC_SAP_PRICING'][ intval($product_id) ] ) ? $GLOBALS['HYTEC_SAP_PRICING'][ intval($product_id) ] : null;
}

// Convenience: get pricing for product (uses in-memory cache or calls endpoint)
function hytec_sap_get_or_fetch_pricing( $product, $user_id ) {
    if ( ! ( $product instanceof WC_Product ) ) return null;
    $cached = hytec_sap_get_last_pricing( $product->get_id() );
    if ( is_array( $cached ) ) return $cached;
    $payload = hytec_build_sales_order_payload( $product, $user_id );
    if ( ! $payload ) return null;
    $response = hytec_call_sap_sales_order( $payload );
    if ( is_wp_error( $response ) ) return null;
    $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
    $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
    $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
    $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
    $data = array( 'net_value' => $net_value, 'sales_tax' => $sales_tax, 'raw' => $response );
    hytec_sap_set_last_pricing( $product->get_id(), $data );
    return $data;
}

function hytec_sap_api_get_url() {
    $val = getenv('HYTEC_SAP_SALES_ORDER_URL');
    if ( empty( $val ) && defined( 'HYTEC_SAP_SALES_ORDER_URL' ) ) {
        $val = HYTEC_SAP_SALES_ORDER_URL;
    }
    if ( $val && filter_var( $val, FILTER_VALIDATE_URL ) ) {
        return $val;
    }
    // Default to provided dev URL (can be overridden via ENV or filter)
    $default = 'https://hytec-dev-8ouoom67.it-cpi034-rt.cfapps.us10-002.hana.ondemand.com/http/WC/SalesOrderS';
    return apply_filters( 'hytec_sap_sales_order_url', $default );
}

function hytec_sap_api_get_auth_header() {
    // 1) Prefer manual token in wp-config if present and not expired
    if ( defined('HYTEC_SAP_API_TOKEN') && HYTEC_SAP_API_TOKEN ) {
        $manual = HYTEC_SAP_API_TOKEN;
        $jwt    = preg_replace('/^Bearer\s+/i','', $manual);
        $exp    = hytec_sap_decode_jwt_exp( $jwt );
        $now    = time();
        if ( $exp && ($exp - 60) > $now ) { // 60s clock skew buffer
            $has_scheme = preg_match('/^(Bearer|Basic)\s+/i', $manual) === 1;
            $header_val = $has_scheme ? $manual : ('Bearer ' . $manual);
            return apply_filters( 'hytec_sap_api_auth_header', $header_val );
        }
        // Falls through to OAuth fetch if expired or unparsable
    }

    // 2) Use cached OAuth access token if available
    $token = hytec_sap_get_cached_oauth_token();
    if ( is_string( $token ) && $token !== '' ) {
        return apply_filters( 'hytec_sap_api_auth_header', 'Bearer ' . $token );
    }

    // 3) Fetch a new one via client_credentials
    $new = hytec_sap_oauth_get_access_token();
    if ( is_wp_error( $new ) ) {
        return '';
    }
    return apply_filters( 'hytec_sap_api_auth_header', 'Bearer ' . $new );
}


// Decode JWT exp (Unix time) without verifying signature (for expiry check only)
function hytec_sap_decode_jwt_exp( $jwt ) {
    $parts = explode('.', (string) $jwt);
    if ( count($parts) !== 3 ) return 0;
    $payload = $parts[1];
    $payload .= str_repeat('=', 3 - (strlen($payload) + 3) % 4); // pad base64url
    $json = json_decode( base64_decode( strtr($payload, '-_', '+/') ), true );
    if ( ! is_array($json) || empty($json['exp']) ) return 0;
    return intval($json['exp']);
}

// Cached token helpers
function hytec_sap_cached_token_key() {
    $id = defined('HYTEC_SAP_OAUTH_CLIENT_ID') ? HYTEC_SAP_OAUTH_CLIENT_ID : 'none';
    $url = defined('HYTEC_SAP_OAUTH_TOKEN_URL') ? HYTEC_SAP_OAUTH_TOKEN_URL : 'none';
    return 'hytec_sap_oauth_' . md5( $id . '|' . $url );
}
function hytec_sap_get_cached_oauth_token() {
    $token = get_transient( hytec_sap_cached_token_key() );
    return is_string($token) ? $token : '';
}
function hytec_sap_set_cached_oauth_token( $token, $ttl ) {
    if ( $ttl > 60 ) { $ttl = $ttl - 60; }
    if ( $ttl <= 0 ) { $ttl = 600; }
    set_transient( hytec_sap_cached_token_key(), (string) $token, intval($ttl) );
}

// OAuth client_credentials fetch
function hytec_sap_oauth_get_access_token() {
    $token_url  = defined('HYTEC_SAP_OAUTH_TOKEN_URL') ? HYTEC_SAP_OAUTH_TOKEN_URL : getenv('HYTEC_SAP_OAUTH_TOKEN_URL');
    $client_id  = defined('HYTEC_SAP_OAUTH_CLIENT_ID') ? HYTEC_SAP_OAUTH_CLIENT_ID : getenv('HYTEC_SAP_OAUTH_CLIENT_ID');
    $client_sec = defined('HYTEC_SAP_OAUTH_CLIENT_SECRET') ? HYTEC_SAP_OAUTH_CLIENT_SECRET : getenv('HYTEC_SAP_OAUTH_CLIENT_SECRET');
    if ( empty($token_url) || empty($client_id) || empty($client_sec) ) {
        return new WP_Error('sap_oauth_config', 'OAuth config missing');
    }
    $headers = array(
        'Authorization' => 'Basic ' . base64_encode( $client_id . ':' . $client_sec ),
        'Content-Type'  => 'application/x-www-form-urlencoded',
        'Accept'        => 'application/json',
    );
    $body = http_build_query( array( 'grant_type' => 'client_credentials' ), '', '&' );
    $resp = wp_remote_post( $token_url, array( 'timeout' => 10, 'headers' => $headers, 'body' => $body ) );
    if ( is_wp_error( $resp ) ) return $resp;
    $code = wp_remote_retrieve_response_code( $resp );
    $raw  = wp_remote_retrieve_body( $resp );
    if ( $code < 200 || $code >= 300 ) {
        return new WP_Error( 'sap_oauth_status', 'OAuth non-2xx: ' . $code, array('body'=>$raw) );
    }
    $json = json_decode( $raw, true );
    if ( ! is_array($json) || empty($json['access_token']) ) {
        return new WP_Error( 'sap_oauth_json', 'OAuth JSON parse/token missing' );
    }
    $ttl = intval( $json['expires_in'] ?? 3600 );
    hytec_sap_set_cached_oauth_token( $json['access_token'], $ttl );
    return (string) $json['access_token'];
}

// Helper: pad SKU to 18 chars if numeric; leave as-is if alphanumeric
function hytec_format_material_id( $sku ) {
    $sku = trim( (string) $sku );
    if ( $sku === '' ) {
        return '';
    }
    if ( ctype_digit( $sku ) ) {
        // Numeric only: left-pad with zeros to 18 chars
        if ( strlen( $sku ) < 18 ) {
            return str_pad( $sku, 18, '0', STR_PAD_LEFT );
        }
        return $sku; // already >= 18
    }
    return $sku; // alphanumeric: do not pad
}

// Helper: pad Customer ID to 10 chars if numeric; leave as-is if alphanumeric
function hytec_format_customer_id( $id ) {
    $id = trim( (string) $id );
    if ( $id === '' ) {
        return '';
    }
    if ( ctype_digit( $id ) ) {
        if ( strlen( $id ) < 10 ) {
            return str_pad( $id, 10, '0', STR_PAD_LEFT );
        }
        return $id; // already >= 10
    }
    return $id; // alphanumeric: do not pad
}

// Fetch customer data for current user from wp_sap_soldto_customers
function hytec_get_sap_customer_for_user( $user_id ) {
    // Prefer existing helper if available
    if ( function_exists( 'sap_cart_get_customer_data' ) ) {
        $row = sap_cart_get_customer_data( $user_id );
        if ( $row ) {
            return $row; // stdClass with fields
        }
    }

    global $wpdb;

    $customer_id = get_user_meta( $user_id, '_customer', true );
    if ( empty( $customer_id ) ) {
        return null;
    }

    $table_name = $wpdb->prefix . 'sap_soldto_customers';
    if ( $wpdb->get_var( $wpdb->prepare( "SHOW TABLES LIKE %s", $table_name ) ) !== $table_name ) {
        return null;
    }

    $row = $wpdb->get_row( $wpdb->prepare(
        "SELECT customer_id, company_code, country_code, price_group, Z7_Partner_no, company, address_line1, address_line2, city, postcode, country_region, state_county FROM {$table_name} WHERE customer_id = %s",
        $customer_id
    ) );

    return $row ?: null;
}

function hytec_build_sales_order_payload( $product, $user_id, $quantity = 1 ) {
    $sku = $product instanceof WC_Product ? $product->get_sku() : '';
    $material_id = hytec_format_material_id( $sku );

    hytec_sap_console_log('log', 'Building SAP payload', array(
        'sku' => $sku,
        'material_id' => $material_id,
        'user_id' => $user_id,
        'quantity' => $quantity,
    ));

    if ( $material_id === '' ) {
        hytec_sap_console_log('warn', 'Empty material_id (SKU missing on product)');
    }

    $user = get_userdata( $user_id );
    $first_name = get_user_meta( $user_id, 'first_name', true );
    $last_name  = get_user_meta( $user_id, 'last_name', true );
    $email      = $user ? $user->user_email : '';

    $customer_meta = get_user_meta( $user_id, '_customer', true );
    $sap_customer = hytec_get_sap_customer_for_user( $user_id );

    hytec_sap_console_log('log', 'SAP customer lookup', array(
        '_customer_meta' => $customer_meta,
        'found' => (bool) $sap_customer,
    ));

    if ( ! $sap_customer ) {
        hytec_sap_console_log('warn', 'No SAP customer row for user');
        return null;
    }

    $billing = array(
        'billing_id'    => hytec_format_customer_id( (string) ( $sap_customer->customer_id ?? '' ) ),
        'Z7_Partner_no' => (string) ( $sap_customer->Z7_Partner_no ?? '' ),
        'first_name'    => (string) $first_name,
        'last_name'     => (string) $last_name,
        'company_code'  => (string) ( $sap_customer->company_code ?? '' ),
        'company_name'  => (string) ( $sap_customer->company ?? '' ),
        'address_1'     => (string) ( $sap_customer->address_line1 ?? '' ),
        'address_2'     => (string) ( $sap_customer->address_line2 ?? '' ),
        'city'          => (string) ( $sap_customer->city ?? '' ),
        'state'         => (string) ( $sap_customer->state_county ?? '' ),
        'postcode'      => (string) ( $sap_customer->postcode ?? '' ),
        'country'       => (string) ( $sap_customer->country_code ?? $sap_customer->country_region ?? '' ),
        'email'         => (string) $email,
    );

    $currency = function_exists('get_woocommerce_currency') ? get_woocommerce_currency() : 'USD';
    $timestamp = current_time( 'timestamp' );
    $date_iso  = date_i18n( 'Y-m-d\TH:i:s', $timestamp );

    $payload = array(
        'order' => array(
            'date_created' => $date_iso,
            'currency'     => $currency,
            'billing'      => $billing,
            'line_items'   => array(
                array(
                    'product_id' => $material_id,
                    'quantity'   => (floor($quantity) == $quantity) ? $quantity * 1000 : number_format($quantity, 4, '.', ''),
                )
            ),
        ),
    );

    // Console: print current user and billing info for debugging
    hytec_sap_console_log('log', 'Current user context', array(
        'user_id'    => $user_id,
        'email'      => $email,
        'first_name' => $first_name,
        'last_name'  => $last_name,
        'billing'    => $billing,
    ));

    return $payload;
}

function hytec_call_sap_sales_order( $payload ) {
    $url = hytec_sap_api_get_url();
    $auth = hytec_sap_api_get_auth_header();

    if ( empty( $url ) || empty( $auth ) ) {
        $extra = '';
        if ( empty( $auth ) ) {
            $extra = hytec_sap_oauth_last_error();
        }
        error_log( 'SAP Product Pricing: Missing URL or Authorization token.' . ( $extra ? (' ' . $extra) : '' ) );
        return new WP_Error( 'sap_config', 'Missing SAP configuration.', array( 'body' => $extra ) );
    }

    $args = array(
        'method'      => 'POST',
        'timeout'     => 10,
        'headers'     => array(
            'Content-Type'  => 'application/json',
            // Mask Authorization in request debug copy below
            'Authorization' => $auth,
        ),
        'body'        => wp_json_encode( $payload ),
        'data_format' => 'body',
    );

    // Save a sanitized request snapshot for on-page debug and file log
    $debug_headers = hytec_sap_sanitize_headers_for_log( $args['headers'] );
    $request_snapshot = array(
        'method'  => 'POST',
        'url'     => $url,
        'headers' => $debug_headers,
        'body'    => $args['body'],
    );
    hytec_sap_set_last_request_debug( $request_snapshot );
    hytec_sap_write_log( 'request', $request_snapshot );

    $response = wp_remote_post( $url, $args );

    if ( is_wp_error( $response ) ) {
        error_log( 'SAP Product Pricing: Request error - ' . $response->get_error_message() );
        hytec_sap_write_log( 'error', array( 'type' => 'http', 'message' => $response->get_error_message() ) );
        return $response;
    }

    $code = wp_remote_retrieve_response_code( $response );
    $body = wp_remote_retrieve_body( $response );

    // Log raw response (sanitize nothing needed here but keep payload small if huge)
    hytec_sap_write_log( 'response', array( 'status' => $code, 'body' => $body ) );

    if ( $code < 200 || $code >= 300 ) {
        error_log( 'SAP Product Pricing: Non-2xx response (' . $code . '): ' . substr( $body, 0, 500 ) );
        return new WP_Error( 'sap_http', 'Unexpected response code: ' . $code, array( 'body' => $body ) );
    }

    $json = json_decode( $body, true );
    if ( json_last_error() !== JSON_ERROR_NONE ) {
        error_log( 'SAP Product Pricing: JSON decode error - ' . json_last_error_msg() );
        hytec_sap_write_log( 'error', array( 'type' => 'json', 'message' => json_last_error_msg(), 'raw' => substr( (string) $body, 0, 1000 ) ) );
        return new WP_Error( 'sap_json', 'Invalid JSON response.' );
    }

    return $json;
}

function hytec_render_sap_pricing_block() {
    hytec_sap_console_log('log', 'SAP pricing hook fired', array('is_product'=>is_product(), 'is_user_logged_in'=>is_user_logged_in()));
    if ( ! is_product() ) {
        return;
    }

    global $product;
    if ( ! $product instanceof WC_Product ) {
        hytec_sap_console_log('warn', 'Global $product not WC_Product');
        return;
    }

    // Always render a section so we can see something even if API is unavailable
    $render_placeholder = true;

    if ( ! is_user_logged_in() ) {
        hytec_sap_console_log('info', 'User not logged in; rendering placeholder');
        if ( hytec_sap_debug_ui_enabled() ) hytec_render_pricing_section( '—', '—', 'Login required for SAP pricing');
        return;
    }

    $user_id = get_current_user_id();

    $payload = hytec_build_sales_order_payload( $product, $user_id );
    if ( ! $payload ) {
        hytec_sap_console_log('warn', 'No payload (likely missing Sold-To mapping for user)', array('user_id'=>$user_id));
        if ( hytec_sap_debug_ui_enabled() ) hytec_render_pricing_section( '—', '—', 'Customer mapping not found');
        return;
    }

    hytec_sap_console_log('log', 'Calling SAP pricing endpoint', array('url'=> hytec_sap_api_get_url() ));
    $response = hytec_call_sap_sales_order( $payload );

    if ( is_wp_error( $response ) ) {
        hytec_sap_console_log('error', 'SAP pricing call failed', array('code'=>$response->get_error_code(), 'msg'=>$response->get_error_message()));
        if ( hytec_sap_debug_ui_enabled() ) {
            hytec_render_pricing_section( '—', '—', 'SAP pricing unavailable');
            $err_data = $response->get_error_data();
            $raw_body = '';
            if ( is_array( $err_data ) && isset( $err_data['body'] ) ) {
                $raw_body = (string) $err_data['body'];
            }
            $debug_output = $raw_body !== '' ? $raw_body : ( $response->get_error_code() . ': ' . $response->get_error_message() );
            echo '<details class="sap-pricing-debug" style="margin-top:8px;"><summary>SAP API Response (debug)</summary><pre style="white-space:pre-wrap;max-height:320px;overflow:auto;">' . esc_html( $debug_output ) . '</pre></details>';
            hytec_sap_print_request_debug();
        }
        return;
    }

    $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
    $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
    $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
    $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';

    hytec_sap_console_log('log', 'SAP pricing success', array('net_value'=>$net_value, 'sales_tax'=>$sales_tax));

    hytec_render_pricing_section( $net_value, $sales_tax );
    // Also print the full request (sanitized) for debugging, and the raw response JSON
    if ( hytec_sap_debug_ui_enabled() ) {
        hytec_sap_print_request_debug();
        echo '<details class="sap-response-debug" style="margin-top:8px;"><summary>SAP API Response (debug)</summary><pre style="white-space:pre-wrap;max-height:400px;overflow:auto;">' . esc_html( print_r( $response, true ) ) . '</pre></details>';
    }
}
add_action( 'woocommerce_single_product_summary', 'hytec_render_sap_pricing_block', 29 );

// Optional: Shortcode for debugging
add_shortcode( 'hytec_sap_pricing_debug', function() {
    if ( ! current_user_can( 'manage_options' ) ) {
        return '';
    }

    if ( ! is_product() ) {
        return '<em>Not on a product page.</em>';
    }

    global $product;
    $payload = hytec_build_sales_order_payload( $product, get_current_user_id() );
    if ( ! $payload ) {
        return '<em>No SAP customer data for this user.</em>';
    }

    $res = hytec_call_sap_sales_order( $payload );
    return '<pre>' . esc_html( print_r( $res, true ) ) . '</pre>';
} );

 