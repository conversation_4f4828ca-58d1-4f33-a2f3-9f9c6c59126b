<?php
/**
 * Test script for SAP ShipTo Addresses API
 * This script tests the new functionality where ship-to addresses are linked to sold-to customers
 */

// Load WordPress
if (!function_exists('get_user_meta')) {
    if (file_exists('./wp-config.php')) {
        require_once('./wp-config.php');
    } elseif (file_exists('../wp-config.php')) {
        require_once('../wp-config.php');
    } else {
        die('WordPress not found.');
    }
}

echo "<h1>SAP ShipTo Addresses API Test (New Logic)</h1>\n";
echo "<p>This test demonstrates the new logic where ship-to addresses are linked to sold-to customers via the shiptos array.</p>\n";

// Test data with ship-to addresses
$shipto_test_data = [
    "shipTo" => [
        [
            "customerId" => "0001129186",
            "companyCode" => "3090",
            "identifier" => "Hydraulics Supply Co",
            "isDefaultAddress" => true,
            "companyName" => null,
            "country" => "US",
            "address" => [
                "street" => "7680 Central Industrial Drive",
                "apartment" => null,
                "city" => "West Palm Beach",
                "postalCode" => "33404-3432"
            ],
            "stateCounty" => "FL"
        ],
        [
            "customerId" => "0001126769",
            "companyCode" => "3090",
            "identifier" => "Hydraulic Supply Company_1",
            "isDefaultAddress" => false,
            "companyName" => null,
            "country" => "US",
            "address" => [
                "street" => "300 International Parkway_1",
                "apartment" => "",
                "city" => "Sunrise_1",
                "postalCode" => "33325-6240"
            ],
            "stateCounty" => "US"
        ]
    ]
];

// Sample sold-to data that references the ship-to addresses
$soldto_test_data = [
    "soldTo" => [
        "customerId" => "0001126769",
        "companyCode" => "3090",
        "countryCode" => "US",
        "priceGroup" => "96",
        "Z7_Partner_no" => "0000017669"
    ],
    "billingAddress" => [
        "company" => "Hydraulic Supply Company_1",
        "address" => [
            "line1" => "300 International Parkway_1",
            "line2" => "",
            "city" => "Sunrise_1",
            "postcode" => "33325-6240",
            "countryRegion" => "US",
            "stateCounty" => "US"
        ]
    ],
    "shiptos" => [
        "0001126769",
        "0001129186"
    ]
];

echo "<h2>Ship-To Test Data</h2>\n";
echo "<pre>" . json_encode($shipto_test_data, JSON_PRETTY_PRINT) . "</pre>\n";

echo "<h2>Sold-To Test Data</h2>\n";
echo "<pre>" . json_encode($soldto_test_data, JSON_PRETTY_PRINT) . "</pre>\n";

// Check if we have a test user with the sold-to customerId
echo "<h2>Checking for Test User (Sold-To Customer)</h2>\n";

$soldto_customer_id = ltrim($soldto_test_data['soldTo']['customerId'], '0');
$test_user = get_users([
    'meta_key' => '_customer',
    'meta_value' => $soldto_customer_id,
    'number' => 1
]);

if (empty($test_user)) {
    echo "<p>❌ No user found with sold-to customerId '{$soldto_customer_id}'</p>\n";
    echo "<p>Creating a test user...</p>\n";

    // Create test user
    $user_data = [
        'user_login' => 'soldto_testuser_' . time(),
        'user_email' => 'soldto_test_' . time() . '@example.com',
        'user_pass' => wp_generate_password(),
        'role' => 'b2b_customer'
    ];

    $user_id = wp_insert_user($user_data);

    if (is_wp_error($user_id)) {
        echo "<p>❌ Failed to create test user: " . $user_id->get_error_message() . "</p>\n";
        exit;
    }

    // Add customer ID meta
    update_user_meta($user_id, '_customer', $soldto_customer_id);

    echo "<p>✅ Created test user with ID: {$user_id} for sold-to customer: {$soldto_customer_id}</p>\n";
} else {
    $user_id = $test_user[0]->ID;
    echo "<p>✅ Found existing test user with ID: {$user_id} for sold-to customer: {$soldto_customer_id}</p>\n";
}

// Show current addresses before processing
echo "<h2>Current Addresses (Before)</h2>\n";
$current_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
if (empty($current_addresses)) {
    echo "<p>No existing addresses</p>\n";
} else {
    echo "<pre>" . print_r($current_addresses, true) . "</pre>\n";
}

// Test the conversion function
echo "<h2>Testing Address Conversion</h2>\n";

// Load the plugin functions
if (function_exists('convert_sap_to_wcmca_format')) {
    foreach ($shipto_test_data['shipTo'] as $index => $sap_address) {
        echo "<h3>Ship-To Address " . ($index + 1) . "</h3>\n";
        echo "<h4>SAP Format:</h4>\n";
        echo "<pre>" . print_r($sap_address, true) . "</pre>\n";

        $wcmca_format = convert_sap_to_wcmca_format($sap_address);

        echo "<h4>WCMCA Format:</h4>\n";
        echo "<pre>" . print_r($wcmca_format, true) . "</pre>\n";
    }
} else {
    echo "<p>❌ Plugin functions not loaded. Make sure the plugin is activated.</p>\n";
}

echo "<h2>New Workflow Explanation</h2>\n";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #0073aa;'>\n";
echo "<h3>How the New Logic Works:</h3>\n";
echo "<ol>\n";
echo "<li><strong>Ship-To API Call:</strong> Ship-to addresses are sent to <code>/wp-json/wc/v3/sap-shipto</code> and stored in the database</li>\n";
echo "<li><strong>Sold-To API Call:</strong> Sold-to customer data is sent to <code>/wp-json/wc/v3/sap-soldto</code> with a 'shiptos' array</li>\n";
echo "<li><strong>Automatic Linking:</strong> The ship-to API automatically finds all sold-to customers and updates their WordPress users with the appropriate ship-to addresses based on the 'shiptos' array</li>\n";
echo "</ol>\n";
echo "<p><strong>Example:</strong> If a sold-to customer has <code>\"shiptos\": [\"1126769\", \"1129186\"]</code>, their WordPress user will get all addresses from both ship-to customers 1126769 and 1129186.</p>\n";
echo "</div>\n";

// Simulate the workflow
echo "<h2>Simulating the New Workflow</h2>\n";

// Step 1: Simulate sold-to data being stored (this would normally come from the sold-to API)
global $wpdb;
$soldto_table = $wpdb->prefix . 'sap_soldto_customers';

echo "<h3>Step 1: Simulating Sold-To Data Storage</h3>\n";
$soldto_data = [
    'customer_id' => $soldto_customer_id,
    'company_code' => $soldto_test_data['soldTo']['companyCode'],
    'country_code' => $soldto_test_data['soldTo']['countryCode'],
    'price_group' => $soldto_test_data['soldTo']['priceGroup'],
    'shiptos' => json_encode(array_map(function($id) { return ltrim($id, '0'); }, $soldto_test_data['shiptos'])),
    'wp_user_id' => $user_id,
    'updated_at' => current_time('mysql')
];

$existing = $wpdb->get_row($wpdb->prepare("SELECT id FROM {$soldto_table} WHERE customer_id = %s", $soldto_customer_id));
if ($existing) {
    $wpdb->update($soldto_table, $soldto_data, ['customer_id' => $soldto_customer_id]);
    echo "<p>✅ Updated existing sold-to record for customer {$soldto_customer_id}</p>\n";
} else {
    $soldto_data['created_at'] = current_time('mysql');
    $wpdb->insert($soldto_table, $soldto_data);
    echo "<p>✅ Created new sold-to record for customer {$soldto_customer_id}</p>\n";
}

// Step 2: Test the ship-to API processing
echo "<h3>Step 2: Testing Ship-To API Processing</h3>\n";
if (function_exists('sap_shipto_update_soldto_users_with_shiptos')) {
    // Get the ship-to customer IDs from our test data
    $shipto_ids = [];
    foreach ($shipto_test_data['shipTo'] as $shipto) {
        $shipto_ids[] = ltrim($shipto['customerId'], '0');
    }

    echo "<p>Processing ship-to IDs: " . implode(', ', $shipto_ids) . "</p>\n";
    $result = sap_shipto_update_soldto_users_with_shiptos($shipto_ids);
    echo "<p>✅ Ship-to processing result:</p>\n";
    echo "<pre>" . print_r($result, true) . "</pre>\n";
} else {
    echo "<p>❌ Ship-to processing function not available</p>\n";
}

// Show final addresses after processing
echo "<h2>Final Addresses (After New Logic)</h2>\n";
$final_addresses = get_user_meta($user_id, '_wcmca_additional_addresses', true);
if (empty($final_addresses)) {
    echo "<p>No addresses found</p>\n";
} else {
    echo "<pre>" . print_r($final_addresses, true) . "</pre>\n";
    echo "<p>Total addresses: " . count($final_addresses) . "</p>\n";
}

// Test the REST API endpoints
echo "<h2>Testing REST API Endpoints</h2>\n";
echo "<h3>1. Ship-To Endpoint</h3>\n";
echo "<p>Send ship-to addresses to: <code>" . home_url('/wp-json/wc/v3/sap-shipto') . "</code></p>\n";
echo "<pre>" . json_encode($shipto_test_data, JSON_PRETTY_PRINT) . "</pre>\n";

echo "<h3>2. Sold-To Endpoint</h3>\n";
echo "<p>Send sold-to data to: <code>" . home_url('/wp-json/wc/v3/sap-soldto') . "</code></p>\n";
echo "<pre>" . json_encode($soldto_test_data, JSON_PRETTY_PRINT) . "</pre>\n";

echo "<h2>cURL Commands for Testing</h2>\n";
echo "<h3>Ship-To API:</h3>\n";
echo "<pre>";
echo "curl -X POST \\\n";
echo "  '" . home_url('/wp-json/wc/v3/sap-shipto') . "' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -H 'Authorization: Basic [YOUR_AUTH_HERE]' \\\n";
echo "  -d '" . json_encode($shipto_test_data) . "'";
echo "</pre>\n";

echo "<h3>Sold-To API:</h3>\n";
echo "<pre>";
echo "curl -X POST \\\n";
echo "  '" . home_url('/wp-json/wc/v3/sap-soldto') . "' \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -H 'Authorization: Basic [YOUR_AUTH_HERE]' \\\n";
echo "  -d '" . json_encode($soldto_test_data) . "'";
echo "</pre>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>Check the WordPress error logs and the SAP API logs in <code>/wp-content/logs/</code> for detailed processing information.</p>\n";
?>
