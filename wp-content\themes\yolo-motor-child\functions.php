<?php

/**
 * Theme functions for YOLO Framework.
 * This file include the framework functions, it should remain intact between themes.
 * For theme specified functions, see file functions-<theme name>.php
 *
 * @package    YoloTheme
 * @version    1.0.0
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2016, YoloTheme
 * @license    http://opensource.org/licenses/gpl-2.0.php GPL v2 or later
 * @link       http://yolotheme.com
 */
// define( 'YOLO_SCRIPT_DEBUG', true); // use for developer only - delete or comment when finish
// ini_set('xdebug.max_nesting_level', 500); // Fix xdebug Fatal error: Maximum function nesting level of '100' reached, aborting! (need change in php.ini and delete here)

$uploaded_file_name = '';

// Define missing parent theme function to prevent errors
if ( ! function_exists( 'yolo_include_footer_id' ) ) {
    function yolo_include_footer_id() {
        return '';
    }
}

// Include SAP pricing functions
require_once get_stylesheet_directory() . '/functions-sap-pricing.php';

add_action('init', function () {
    error_log('user is not logged in for class&&');
    if (is_user_logged_in()) {
        error_log('user is logged in for class&&');
        // Include your custom product class
        require_once get_stylesheet_directory() . '/woocommerce/includes/class-my-custom-product.php';
    }
});

add_filter('woocommerce_product_class', function ($classname, $product_type, $product_id) {
    error_log('classes loading &&');
    // Replace WC_Product with My_Custom_Product
    if ($classname === 'WC_Product_Simple') {
        error_log('loaded&&');
        return 'WC_Custom_Product';
    }
    return $classname;
}, 10, 3);

function yolo_enqueue_parent_styles()
{
    wp_enqueue_style('child-style', get_stylesheet_directory_uri() . '/style.css');
    wp_enqueue_style('select2-style', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
    wp_enqueue_style('datatables-css', 'https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css');
    wp_enqueue_script('datatables-js', 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js', array('jquery'), null, true);
    wp_enqueue_script('select2-js', 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js', array('jquery'), null, true);
}

add_action('wp_enqueue_scripts', 'yolo_enqueue_parent_styles', 9999);
add_filter('woocommerce_admin_disabled', '__return_true');
add_filter('woocommerce_marketing_menu_items', '__return_empty_array');
add_filter('woocommerce_admin_features', function ($features) {
    $marketing_key = array_search('marketing', $features);
    if (false !== $marketing_key) {
        unset($features[$marketing_key]);
    }
    return $features;
});
add_filter('woocommerce_admin_daily', '__return_false');
add_filter('woocommerce_admin_hourly', '__return_false');

add_action('wp_head', 'define_ajax_url');
function define_ajax_url()
{
    ?>
    <script type="text/javascript">
        var ajaxurl = "<?php echo admin_url('admin-ajax.php'); ?>";
    </script>
    <?php
}

add_action('wp_ajax_fetch_products', 'fetch_products_ajax');
add_action('wp_ajax_nopriv_fetch_products', 'fetch_products_ajax');
function fetch_products_ajax()
{
    error_log("fetch_products_ajax called");
    global $total_get_data;
    $search_value = isset($_POST['search']['value']) ? sanitize_text_field($_POST['search']['value']) : '';
    $in_stock_only = isset($_POST['inStockOnly']) && $_POST['inStockOnly'] === 'true';
    $sort_by = isset($_POST['sortBy']) ? sanitize_text_field($_POST['sortBy']) : 'default';
    $suffix = get_stock_suffix();
    $meta_query = [];

    // Updated "In Stock Only" filter to use customer-based stock fields
    if ($in_stock_only) {
        // Get customer location for stock determination
        $main_user_id = get_current_user_id();
        $company_code = get_user_meta($main_user_id, '_companycode', true);
        $is_us_customer = ($company_code == "3090");

        $stock_meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';

        $meta_query[] = [
            'key' => $stock_meta_key,
            'value' => 0,
            'compare' => '>',
        ];

        // Old filter logic - commented for future reference
        // $meta_query[] = [
        //     'key' => '_stock_status',
        //     'value' => 'instock',
        //     'compare' => '=',
        // ];
    }

    $price_code_filtering_ids = [];
    $ce_approved_filter = [];

    foreach ($_POST['customFields'] as $key => $values) {
        if (!empty($values)) {
            if ($key == 'ce_approved') {
                $ce_approved_filter = $values;
            } elseif ($key !== 'usd_price_code') { // ignore deprecated price code filter
                $meta_query[] = [
                    'key' => '_' . $key,
                    'value' => $values,
                    'compare' => 'IN',
                ];
            }
        }
    }

    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    // Price code filtering removed from products table
    $get_data = get_product_for_customer_by_id($main_user_id, [], $ce_approved_filter); // Retrieve filtered product IDs without price code filter

    // var_dump($get_data);
    // exit;
    $_SESSION['total_get_data'] = $get_data;
    $filtered_ids_new = $get_data['new_price_products'];
    $filtered_ids_price = $get_data['allowed_products'];
    $total_price_codes = $get_data['allowed_price_codes'];
    $filtered_ids = array_merge($filtered_ids_new, $filtered_ids_price);

    $args = [];
    $query = [];
    $products = [];

    if (empty($filtered_ids)) {
        $query = []; // Return an empty array or handle accordingly
    } else {
        // Handle DataTables sorting
        $orderby = 'post__in'; // Default ordering
        $order = 'ASC';
        $meta_key = '';

        if (isset($_POST['order']) && !empty($_POST['order'])) {
            $order_column = intval($_POST['order'][0]['column']);
            $order_dir = sanitize_text_field($_POST['order'][0]['dir']);

            // Map DataTables columns to WordPress orderby values and meta keys
            $column_config = [
                1 => ['orderby' => 'title', 'meta_key' => ''],
                2 => ['orderby' => 'meta_value_num', 'meta_key' => '_stock_us'], // Will be dynamic based on customer
                3 => ['orderby' => 'meta_value_num', 'meta_key' => '_regular_price'],
                4 => ['orderby' => 'post__in', 'meta_key' => ''], // Net price - not sortable, use default
                5 => ['orderby' => 'meta_value', 'meta_key' => '_brand'],
                6 => ['orderby' => 'meta_value', 'meta_key' => '_product_line'],
                7 => ['orderby' => 'meta_value', 'meta_key' => '_product_family'],
                8 => ['orderby' => 'meta_value', 'meta_key' => '_model_size'],
                9 => ['orderby' => 'meta_value', 'meta_key' => '_ce_approved']
            ];

            if (isset($column_config[$order_column])) {
                $config = $column_config[$order_column];
                $orderby = $config['orderby'];
                $meta_key = $config['meta_key'];
                $order = strtoupper($order_dir);

                // Handle stock column - use customer-specific stock field
                if ($order_column == 2) {
                    $main_user_id = get_current_user_id();
                    $company_code = get_user_meta($main_user_id, '_companycode', true);
                    $is_us_customer = ($company_code == "3090");
                    $meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';
                }
            }
        }

        $args = [
            'post__in' => $filtered_ids,
            'post_type' => 'product',
            'posts_per_page' => isset($_POST['length']) ? intval($_POST['length']) : 10,
            'paged' => isset($_POST['start']) ? (intval($_POST['start']) / intval($_POST['length']) + 1) : 1,
            's' => $search_value,
            'meta_query' => $meta_query,
            'orderby' => $orderby,
            'order' => $order
        ];

        // Add meta_key if we're sorting by meta values
        if (!empty($meta_key)) {
            $args['meta_key'] = $meta_key;
        }

        $query = new WP_Query($args);
        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                global $product;

                // Price code filtering removed for products table view
                // $check_price_code = get_filtered_product_price_code($product->get_SKU(), $price_code_filtering_ids, $ce_approved_filter);
                // if (!$check_price_code) {
                //     continue;
                // }
                $num = rand(0, 500);
                $number = ceil($num / 10) * 20;

                $description = get_post_meta($product->get_id(), '_description', true);
                // var_dump($description);
                // exit;
                $products[] = [
                    'quantity' => '<div class="flex aic flex-column mx-auto" ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'data-tooltip="Not Available from Stock"') .  */'>
                                    <div class="single-shop-item flex aic">
                                        <div class="custom-check ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'disabledd') .  */'">
                                            <input type="checkbox" name="products[]" value="' . get_the_ID() . '" id="product_id_' . get_the_ID() . '" />
                                            <label for="product_id_' . get_the_ID() . '"></label>
                                        </div>
                                        <div class="quantity-wrap ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'disabledd') .  */'">
                                            <span class="minus">-</span>
                                            <input type="text" name="quantity" value="1">
                                            <span class="plus">+</span>
                                        </div>
                                    </div>
                                    <div class="custom-button w100 text-center">
                                        <button class="orange-bg white w100 no-border py-1 add_to_cart_button ' . /* (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? '' : 'disabledd') .  */'" data-product-id="' . get_the_ID() . '" data-quantity="1">Add To Cart</button>
                                    </div>
                                   </div>',
                    'title' => '<a href="' . get_permalink($product->get_id()) . '">
                                        <div class="flex aic">
                                           <span class="table-product-image">' . (has_post_thumbnail() ? get_the_post_thumbnail(get_the_ID(), 'thumbnail') : "No Photo Available") . '</span>
                                           <div class="ml-1">
                                               <p class="table-product-title mb-0">' . get_the_title() . '</p>
                                               <p class="table-product-description mb-0">' . $description . '</p>
                                           </div>
                                       </div>
                                    </a>', // get_the_title(),
                    // 'stock'   => '<p class="table-product-stock mb-0"><span class="orange">' . get_post_meta(get_the_ID(), '_stock', true) . '</span> In Stock</p>
                    // Old stock logic - commented for future reference
                    // 'stock' => '<p class="table-product-stock mb-0">' . get_post_meta(get_the_ID(), '_stock_' . $suffix, true) . '</p>
                    // 'stock' => '<p class="table-product-stock mb-0">' . (get_post_meta(get_the_ID(), '_stock_' . $suffix, true) && (int) get_post_meta(get_the_ID(), '_stock_' . $suffix, true) > 0 ? 'Typically In Stock </p>
                    //               <p class="small mb-0">Ships in 1 day or less</p>' : 'Not Available from Stock</p>'), // $product->get_price_html(),

                    // New stock logic - show actual numbers based on customer location
                    'stock' => (function() {
                        $main_user_id = get_current_user_id();
                        $company_code = get_user_meta($main_user_id, '_companycode', true);
                        $is_us_customer = ($company_code == "3090");

                        $stock_meta_key = $is_us_customer ? '_stock_us' : '_stock_eu';
                        $live_stock = get_post_meta(get_the_ID(), $stock_meta_key, true);
                        $live_stock = intval($live_stock);

                        // Show 0 instead of negative numbers
                        $display_stock = max(0, $live_stock);

                        return '<p class="table-product-stock mb-0">' . $display_stock . '</p>';
                    })(),
                    'price' => '<p class="table-product-stock mb-0">'.$product->get_price_html().'</p>',
                    'net_price' => '<div class="sap-net-price-loading" data-sku="' . esc_attr($product->get_sku()) . '">Loading...</div>',
                    'brand' => get_post_meta(get_the_ID(), '_brand', true),
                    'product_line' => get_post_meta(get_the_ID(), '_product_line', true),
                    'product_family' => get_post_meta(get_the_ID(), '_product_family', true),
                    'model_size' => get_post_meta(get_the_ID(), '_model_size', true),
                    'ce_approver' => get_post_meta(get_the_ID(), '_ce_approved', true),
                ];
            }
            wp_reset_postdata();
        }
    }

    // $args = [
    //     'post__in' => $filtered_ids,
    //     'post_type' => $post_type,
    //     'posts_per_page' => isset($_POST['length']) ? $_POST['length'] : 10,
    //     'paged' => isset($_POST['start']) ? (intval($_POST['start']) / intval($_POST['length']) + 1) : 1,
    //     's' => $search_value,
    //     'meta_query' => $meta_query,
    //     //'orderby'        => $sort_by === 'default' ? 'date' : $sort_by,
    //     'orderby' => 'post__in'
    // ];
    // // var_dump($args);
    // // exit;

    // // Execute the product query
    // $query = new WP_Query($args);
    // Loop through the products and format the data

    // Prepare response in DataTables format
    $response = [
        "draw" => intval($_POST['draw']),
        "recordsTotal" => empty($filtered_ids) ? 1 : (isset($query) ? $query->found_posts : 0),
        "recordsFiltered" => empty($filtered_ids) ? 1 : (isset($query) ? $query->found_posts : 0),
        "data" => $products,
        // Debug info - remove after testing
        "debug" => [
            "filtered_ids_count" => count($filtered_ids),
            "products_count" => count($products),
            "query_found_posts" => isset($query) ? $query->found_posts : 0,
            "sample_product" => !empty($products) ? array_keys($products[0]) : [],
            "post_data" => $_POST
        ]
    ];

    wp_send_json($response);
}

function get_filtered_product_price_code($product_sku, $filtering_code_ids, $ce_approvers)
{
    global $wpdb;
    $table_shuffix = "";
    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $customer_id = get_user_meta($main_user_id, '_customer', true);
    if ($customer_id !== "") {
        $company_code = get_user_meta($main_user_id, '_companycode', true);
        $country_code = get_user_meta($main_user_id, '_country', true);
        if ($company_code == "3090") {
            $table_shuffix = "";
        } else {
            if ($country_code == "GB") {
                $table_shuffix = "_gbp";
            } else {
                $table_shuffix = "_eur";
            }
        }

        $_SESSION["table_shuffix"] = $table_shuffix;
        $price_list_table = $wpdb->prefix . 'price_list' . $table_shuffix;
        $price_code_list_table = $wpdb->prefix . 'price_code_list' . $table_shuffix;
        $sql = "
			SELECT pcl.price_code_title FROM $price_list_table pl
			JOIN $price_code_list_table pcl
			ON pl.price_code_id=pcl.id
			WHERE pl.product_sku='$product_sku'";
        $res = $wpdb->get_col($wpdb->prepare($sql));
        if (count($filtering_code_ids) > 0) {
            if (in_array($res[0], $filtering_code_ids)) {
                return $res[0];
            } else {
                return false;
            }
        } else {
            return $res[0] ? $res[0] : "No code";
        }
    }
}
function get_total_price_code()
{
    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $get_data = get_product_for_customer_by_id($main_user_id, [], []); // Retrieve all available product IDs for logged user
    return $get_data['allowed_price_codes'];
}
function get_custom_field_values($field_key)
{
    global $wpdb;

    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $get_data = get_product_for_customer_by_id($main_user_id, [], []); // Retrieve all available product IDs for logged user

    $filtered_ids = array_merge($get_data['new_price_products'], $get_data['allowed_products']);

    // Query distinct values from the postmeta table for the specified custom field
    $results = $wpdb->get_col($wpdb->prepare("
        SELECT DISTINCT meta_value
        FROM {$wpdb->postmeta}
        WHERE post_id IN (" . implode(',', array_map('intval', $filtered_ids)) . ")
		AND meta_key = %s
        AND meta_value IS NOT NULL
        ORDER BY meta_value ASC
    ", $field_key));

    return $results ? array_filter($results) : [];
}

add_action('wp_enqueue_scripts', 'enqueue_custom_scripts');
function enqueue_custom_scripts()
{
    $ver = filemtime(get_stylesheet_directory() . '/assets/js/custom-datatables.js');
    // Enqueue the DataTable initialization script
    wp_enqueue_script('custom-datatables', get_stylesheet_directory_uri() . '/assets/js/custom-datatables.js?v=' . $ver, array('jquery'), null, true);

    // Fetch distinct values for each custom field and pass them to JavaScript
    $custom_field_data = [
        'brand' => get_custom_field_values('_brand'),
        'product_line' => get_custom_field_values('_product_line'),
        'product_family' => get_custom_field_values('_product_family'),
        'product_series' => get_custom_field_values('_product_series'),
        'model_size' => get_custom_field_values('_model_size'),
        'usd_price_code' => get_total_price_code(),
        'ce_approved' => ["Yes", "No"],
        // 'ce_approved' => get_custom_field_values('ce_approved'),
    ];

    // Use wp_localize_script to make data accessible to JavaScript
    wp_localize_script('custom-datatables', 'customFilterData', $custom_field_data);

    // Also pass the AJAX URL
    wp_localize_script('custom-datatables', 'ajax_object', array('ajax_url' => admin_url('admin-ajax.php')));
}

// ADD TO CART
add_action('wp_enqueue_scripts', 'enqueue_custom_ajax_script');
function enqueue_custom_ajax_script()
{
    $ver = filemtime(get_stylesheet_directory() . '/assets/js/custom-add-to-cart.js');
    wp_enqueue_script('custom-ajax-add-to-cart', get_stylesheet_directory_uri() . '/assets/js/custom-add-to-cart.js?v=' . $ver, array('jquery'), null, true);
    wp_localize_script('custom-ajax-add-to-cart', 'customAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('custom-ajax-nonce')
    ));
}

add_action('wp_ajax_custom_add_to_cart', 'custom_add_to_cart');
add_action('wp_ajax_nopriv_custom_add_to_cart', 'custom_add_to_cart');
function custom_add_to_cart()
{
    check_ajax_referer('custom-ajax-nonce', 'nonce');

    $product_id = intval($_POST['product_id']);
    $quantity = intval($_POST['quantity']);

    $product = wc_get_product($product_id);
    if (!$product || !$product->is_purchasable()) {
        wp_send_json_error(array('message' => 'Invalid product.'));
    }

    $suffix = get_stock_suffix();
    $count_before_cart = get_post_meta($product_id, '_stock_' . $suffix, true);
    $back_order_status = get_post_meta($product_id, '_backorders_' . $suffix, true);

    my_plugin_custom_log("Count before cart: $count_before_cart, quantity: $quantity, back order status: $back_order_status, \suffix : $suffix, product id: $product_id");

    if ((int)$count_before_cart < (int) $quantity && $back_order_status === 'no') {
        wp_send_json_error(array('message' => 'Sorry, insufficient stock for this region.'));
    } else {
        try{
            $result = WC()->cart->add_to_cart($product_id, $quantity);
            // my_plugin_custom_log("Add result:" . $result);

            if ($result) {
                wc_add_to_cart_message(array($product_id => $quantity), true);
                // if ((int)$count_before_cart < (int) $quantity && $back_order_status === 'yes') {
                //     wp_send_json_success(array('message' => 'Product is available only ' . $count_before_cart . ' and '.(int)$quantity - (int)$count_before_cart.' items are in backorder for your region now!', 'cart_count' => WC()->cart->get_cart_contents_count()));
                // } else {
                //     wp_send_json_success(array('message' => 'Product added to cart successfully!', 'cart_count' => WC()->cart->get_cart_contents_count()));
                // }
                wp_send_json_success(array('message' => 'Product added to cart successfully!', 'cart_count' => WC()->cart->get_cart_contents_count()));
            } else {
                wp_send_json_error(array('message' => 'Failed to add product to cart.'));
            }
        }catch(WCException $e){
            my_plugin_custom_log('Error add cart message , '. $e->getMessage());
        }

    }

    // if ((int)$count_before_cart < (int) $quantity && $back_order_status === 'yes') {
    //     wc_add_notice(__('This product is on backorder for your region.'), 'notice');
    // }

    // if ((int) $count_before_cart == 0) {
    //     wp_send_json_error(array('message' => 'Not available add to cart. Product is in out of stock.'));
    // } else if ((int) $count_before_cart < (int) $quantity) {
    //     wp_send_json_error(array('message' => 'Product is available only ' . $count_before_cart . ' item(s) now.'));
    // } else {
    //     $result = WC()->cart->add_to_cart($product_id, $quantity);

    //     if ($result) {
    //         wc_add_to_cart_message(array($product_id => $quantity), true);
    //         wp_send_json_success(array('message' => 'Product added to cart successfully!', 'cart_count' => WC()->cart->get_cart_contents_count()));
    //     } else {
    //         wp_send_json_error(array('message' => 'Failed to add product to cart.'));
    //     }
    // }

    wp_die();
}

add_action('wp_ajax_custom_recreate_filters', 'custom_recreate_filters');
add_action('wp_ajax_nopriv_custom_recreate_filters', 'custom_recreate_filters');
function fetch_filtered_options()
{
    global $wpdb;

    // Retrieve selected values from AJAX request
    $current_user = wp_get_current_user();
    $get_data = $_SESSION['total_get_data']; // Retrieve all available product IDs for logged user

    $filtered_ids = array_merge($get_data['new_price_products'], $get_data['allowed_products']);
    // Retrieve selected values from AJAX request
    $selected_values = isset($_POST['selectedValues']) ? $_POST['selectedValues'] : [];
    $selected_price_code = $selected_values['usd_price_code'] > 0 ? $selected_values['usd_price_code'] : [];
    // Create a unique cache key based on selected values
    $cache_key = 'filtered_options_' . md5(json_encode($selected_values));
    $filtered_options = get_transient($cache_key);

    if ($filtered_options === false) {
        // Data is not in cache, build it
        $fields = [
            'brand' => '_brand',
            'product_line' => '_product_line',
            'product_family' => '_product_family',
            'product_series' => '_product_series',
            'model_size' => '_model_size',
            // 'usd_price_code' => '_usd_price_code',
            'ce_approved' => '_ce_approved'
        ];

        $filtered_options = [];

        foreach ($fields as $field_id => $meta_key) {
            if (!empty($selected_values)) {
                $meta_query = ['relation' => 'AND'];

                foreach ($selected_values as $key => $values) {
                    if (!empty($values)) {
                        $meta_query[] = [
                            'key' => $fields[$key],
                            'value' => $values,
                            'compare' => 'IN'
                        ];
                    }
                }

                $args = [
                    'post__id' => $filtered_ids,
                    'post_type' => 'product',
                    'posts_per_page' => -1,
                    'meta_query' => $meta_query,
                    'fields' => 'ids'
                ];

                $query = new WP_Query($args);
                $product_ids = $query->posts;
                if (!empty($product_ids)) {
                    // Fetch unique values for the current custom field based on filtered product IDs
                    $results = $wpdb->get_col($wpdb->prepare("
                        SELECT DISTINCT meta_value
                        FROM {$wpdb->postmeta}
                        WHERE meta_key = %s
                        AND post_id IN (" . implode(',', array_map('intval', $product_ids)) . ")
                    ", $meta_key));

                    $filtered_options[$field_id] = $results ? array_filter($results) : [];
                }
            } else {
                // If no values are selected, get all distinct values for the field
                $results = $wpdb->get_col($wpdb->prepare("
                    SELECT DISTINCT meta_value
                    FROM {$wpdb->postmeta}
                    WHERE meta_key = %s
                    AND meta_value IS NOT NULL
                    ORDER BY meta_value ASC
                ", $meta_key));

                $filtered_options[$field_id] = $results ? array_filter($results) : [];
            }
        }

        // This is filtering for price_code
        $table_shuffix = $_SESSION["table_shuffix"];
        $price_list_table = $wpdb->prefix . "price_list" . $table_shuffix;
        $price_code_table = $wpdb->prefix . "price_code_list" . $table_shuffix;

        if (!empty($selected_values)) {
            foreach ($selected_values as $key => $values) {
                if (!empty($values) && $key !== 'usd_price_code') {
                    $meta_query[] = [
                        'key' => $fields[$key],
                        'value' => $values,
                        'compare' => 'IN'
                    ];
                }
            }

            $args = [
                'post__id' => $filtered_ids,
                'post_type' => 'product',
                'posts_per_page' => -1,
                'meta_query' => $meta_query,
                'fields' => 'ids'
            ];

            $query = new WP_Query($args);
            $product_ids = $query->posts;

            if (!empty($product_ids)) {
                $product_ids_str = implode(',', array_map('intval', $product_ids));
                $query = $wpdb->prepare("
                    SELECT DISTINCT pcl.price_code_title
                    FROM {$wpdb->postmeta} pm
                    INNER JOIN {$price_list_table} pl ON pm.meta_value = pl.product_sku
                    INNER JOIN {$price_code_table} pcl ON pl.price_code_id = pcl.id
                    WHERE pm.meta_key = '_sku'
                    AND pm.post_id IN ($product_ids_str)
                ", $product_ids_str);

                $results = $wpdb->get_col($query);
                $filtered_options['usd_price_code'] = $results ? array_filter($results) : [];
            }
        } else {
            $filtered_options['usd_price_code'] = $get_data['allowed_price_codes'];
        }
        // Store the result in transient cache for 15 minutes
        set_transient($cache_key, $filtered_options, 15 * MINUTE_IN_SECONDS);
    }
    wp_send_json($filtered_options);
}
add_action('wp_ajax_fetch_filtered_options', 'fetch_filtered_options');
add_action('wp_ajax_nopriv_fetch_filtered_options', 'fetch_filtered_options');

function get_logged_user_admin_id()
{
    if (is_user_logged_in()) {
        $cur_user = wp_get_current_user();
        $user_roles = $cur_user->roles;

        if (!in_array('b2b_administrator', $user_roles) && !in_array('administrator', $user_roles)) {
            return get_user_meta($cur_user->ID, '_parent_admin_id', true);
        } else {
            return $cur_user->ID;
        }
    }
}

// customize single product hooks
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_title', 5);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_price', 10);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 30);
remove_action('woocommerce_single_product_summary', 'woocommerce_template_single_meta', 40);

add_action('woocommerce_single_product_summary', 'woocommerce_template_single_meta', 5);
// add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_title', 40 );
// add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_price', 10 );
// add_action( 'woocommerce_single_product_summary', 'woocommerce_template_single_excerpt', 20 );
add_action('woocommerce_single_product_summary', 'woocommerce_template_single_add_to_cart', 10);

// Reorder and rename items in My Account sidebar.
add_filter('woocommerce_account_menu_items', 'custom_my_account_menu_items', 99);
function custom_my_account_menu_items($items)
{

    // Rename items
    $items['dashboard'] = __('Dashboard', 'yolo-motor');
    $items['orders'] = __('Orders', 'yolo-motor');
    $items['downloads'] = __('Downloads', 'yolo-motor');
    $items['edit-address'] = __('Addresses', 'yolo-motor');
    $items['edit-account'] = __('Account Details', 'yolo-motor');
    // $items['company-accounts'] = __('Company Accounts', 'yolo-motor');
    $items['shopping-lists'] = __('Shopping Lists', 'yolo-motor');
    $items['customer-logout'] = __('Logout', 'yolo-motor');

    $ordered_items = array();
    $current_user = wp_get_current_user();

    if (in_array('b2b_sales', $current_user->roles)) {
        $ordered_items = array(
            'dashboard' => $items['dashboard'],
            'shopping-lists' => $items['shopping-lists'],
            // 'company-accounts' => $items['company-accounts'],
            'edit-address' => $items['edit-address'],
            'edit-account' => $items['edit-account'],
            'customer-logout' => $items['customer-logout'],
        );
    } else {
        $ordered_items = array(
            'dashboard' => $items['dashboard'],
            'orders' => $items['orders'],
            'shopping-lists' => $items['shopping-lists'],
            // 'company-accounts' => $items['company-accounts'],
            'edit-address' => $items['edit-address'],
            'edit-account' => $items['edit-account'],
            'customer-logout' => $items['customer-logout'],
        );
    }
    return $ordered_items;
}

add_filter('woocommerce_checkout_fields', 'change_billing_details_title', 999);
function change_billing_details_title($fields)
{
    add_filter('woocommerce_checkout_fields', function ($checkout_fields) {
        $checkout_fields['billing']['billing_title'] = 'Contact Information';
        return $checkout_fields;
    });

    return $fields;
}

add_filter('woocommerce_checkout_fields', 'customize_billing_fields');
function customize_billing_fields($fields)
{
    // Only keep email in the billing fields (phone gets removed by another function anyway)
    $allowed_fields = ['billing_email'];

    foreach ($fields['billing'] as $field_key => $field) {
        if (!in_array($field_key, $allowed_fields)) {
            unset($fields['billing'][$field_key]);
        }
    }

    return $fields;
}
add_filter('woocommerce_cart_needs_shipping_address', '__return_true');
add_filter('woocommerce_checkout_fields', 'disable_ship_to_different_address_option');
function disable_ship_to_different_address_option($fields)
{
    // Remove the "Ship to a different address?" checkbox
    add_filter('woocommerce_ship_to_different_address_checked', '__return_true');

    return $fields;
}

add_filter('woocommerce_checkout_fields', 'customize_shipping_fields');
function customize_shipping_fields($fields)
{
    // Set the company field as required to avoid "(optional)"
    if (isset($fields['shipping']['shipping_company'])) {
        $fields['shipping']['shipping_company']['required'] = true;
    }

    return $fields;
}

add_filter('woocommerce_checkout_fields', 'reorder_shipping_fields_priority');
function reorder_shipping_fields_priority($fields)
{
    // Set the priorities for shipping fields to reorder them
    if (isset($fields['shipping'])) {
        $fields['shipping']['shipping_company']['priority'] = 10;  // Company first
        $fields['shipping']['shipping_first_name']['priority'] = 20;  // First name after company
        $fields['shipping']['shipping_last_name']['priority'] = 30;  // Last name after first name
        $fields['shipping']['shipping_address_1']['priority'] = 40;  // Address line 1
        $fields['shipping']['shipping_address_2']['priority'] = 50;  // Address line 2
        $fields['shipping']['shipping_city']['priority'] = 60;  // City
        $fields['shipping']['shipping_postcode']['priority'] = 70;  // Postcode
        $fields['shipping']['shipping_state']['priority'] = 80;  // State/County
        $fields['shipping']['shipping_country']['priority'] = 90;  // Country
    }

    return $fields;
}

add_filter('woocommerce_cart_shipping_method_full_label', 'customize_shipping_label', 10, 2);
function customize_shipping_label($label, $method)
{
    // Replace the shipping label with custom text
    $label = 'Later';
    return $label;
}

add_filter('woocommerce_shipping_package_name', 'customize_shipping_package_name', 10, 3);
function customize_shipping_package_name($package_name, $i, $package)
{
    // Replace the default "Shipping" text with "Delivery"
    return 'Delivery';
}

add_action('wp_footer', 'replace_total_label_js');
function replace_total_label_js()
{
    if (is_checkout()) {
        ?>
        <script>
            function replaceTotalLabel() {
                // Replace "Total" label with "Estimated Subtotal"
                var totalLabels = document.querySelectorAll('.woocommerce-checkout-review-order-table th');
                totalLabels.forEach(function (label) {
                    if (label.innerText.trim() === 'Total') {
                        label.innerText = 'Estimated Subtotal';
                    }
                });

                // Also replace in mini-cart totals if needed
                var totalSummaryLabels = document.querySelectorAll('.cart-subtotal th, .order-total th');
                totalSummaryLabels.forEach(function (label) {
                    if (label.innerText.trim() === 'Total') {
                        label.innerText = 'Estimated Subtotal';
                    }
                });

                document.getElementById('order_review_heading').innerText = 'Order Summary';
                document.querySelector('.woocommerce-privacy-policy-text').innerText = 'Applicable net pricing and shipping with be calculated with your company rep when your order is received on our system.';
            }

            document.addEventListener('DOMContentLoaded', function () {
                replaceTotalLabel(); // Run on page load
            });

            jQuery(document.body).on('updated_checkout', function () {
                replaceTotalLabel(); // Run on checkout update
            });
        </script>
        <?php
    }
}

add_action('woocommerce_review_order_before_submit', 'add_terms_checkbox', 9);
function add_terms_checkbox()
{
    ?>
    <p class="form-row terms">
        <span class="custom-check woocommerce-form__label woocommerce-form__label-for-checkbox checkbox p-0">
            <input type="checkbox" class="woocommerce-form__input-checkbox" name="terms_agreement" id="terms_agreement" />
            <label for="terms_agreement" class="ml-1">I agree to the <a
                    href="https://hydraulictechnologies.com/terms-and-conditions" target="_blank">Terms
                    and Conditions agreement</a>&nbsp;<span class="required">*</span></label>
        </span>
    </p>
    <?php
}

add_action('woocommerce_checkout_process', 'validate_terms_checkbox');
function validate_terms_checkbox()
{
    if (!isset($_POST['terms_agreement'])) {
        wc_add_notice(__('Please read and accept the Terms and Conditions agreement to proceed.'), 'error');
    }
}

add_filter('woocommerce_checkout_terms_and_conditions_message', 'replace_privacy_text');
function replace_privacy_text($text)
{
    // Replace the existing privacy text with custom text
    $custom_text = 'Applicable net pricing and shipping will be calculated with your company rep when your order is received on our system.';
    return $custom_text;
}

add_filter('woocommerce_locate_template', 'force_custom_review_order_template', 10, 3);
function force_custom_review_order_template($template, $template_name, $template_path)
{
    if ($template_name == 'checkout/review-order.php') {
        $custom_template = get_stylesheet_directory() . '/woocommerce/checkout/review-order.php';
        if (file_exists($custom_template)) {
            return $custom_template;
        }
    }
    return $template;
}

add_action('wp_footer', 'customize_cart_page_header_js');
function customize_cart_page_header_js()
{
    if (is_cart()) {
        $cart_count = 0;
        foreach (WC()->cart->get_cart() as $cart_item) {
            $cart_count++; // Count each unique item in the cart
        }
        ?>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                // Select the <h1> element for the cart title
                var cartTitleElement = document.querySelector('h1.page-title');

                if (cartTitleElement) {
                    // Get the number of items in the cart
                    var cartCount = <?php echo $cart_count ?>;

                    // Update the title text
                    if (cartCount > 0) {
                        cartTitleElement.innerHTML = 'Cart <span class="green">' + cartCount + ' items</span>';

                        // Create the Empty Cart link
                        var emptyCartLink = document.createElement('a');
                        emptyCartLink.href = "javascript:;";
                        emptyCartLink.className = 'empty-cart-link';
                        emptyCartLink.id = 'empty-cart-button';
                        emptyCartLink.textContent = 'Empty Cart';
                        emptyCartLink.style.marginLeft = '15px'; // Add some spacing

                        // Append the link next to the cart title
                        cartTitleElement.appendChild(emptyCartLink);
                    } else {
                        cartTitleElement.textContent = 'Cart';
                    }
                }
            });
        </script>
        <?php
    }
}

add_filter('gettext', 'custom_thank_you_text', 20, 3);
function custom_thank_you_text($translated_text, $text, $domain)
{
    if ('woocommerce' === $domain && 'Thank You For Your Order.' === $text) {
        $translated_text = 'Thank you for your oder submission, Your order ID is ********. Your account representative will follow up for order finalization within 2 business days.';
    }
    return $translated_text;
}

// add_filter('woocommerce_order_button_text', 'custom_checkout_button_label');
// function custom_checkout_button_label($button_text)
// {
//     return 'Go To Last Step';
// }

add_filter('gettext', 'custom_no_address_text', 20, 3);
function custom_no_address_text($translated_text, $text, $domain)
{
    if ('woocommerce' === $domain && 'You have not set up this type of address yet.' === $text) {
        $translated_text = 'You do not have the billing address set yet, please contact us.';
    }
    return $translated_text;
}

add_action('wp_ajax_nopriv_update_cart_count', 'update_cart_count');
add_action('wp_ajax_update_cart_count', 'update_cart_count');
function update_cart_count()
{
    $cart_count = 0;
    foreach (WC()->cart->get_cart() as $cart_item) {
        $cart_count++; // Count each unique item in the cart
    }
    echo $cart_count;

    wp_die(); // Always use wp_die() after an AJAX function to end execution.
}

include 'functions-kekacbre.php';

function lf_custom_query_vars($vars)
{
    $vars['view-order'] = 'view-order';
    return $vars;
}
add_filter('woocommerce_get_query_vars', 'lf_custom_query_vars', 0);

function lf_custom_flush_rewrite_rules()
{
    flush_rewrite_rules();
}
add_action('wp_loaded', 'lf_custom_flush_rewrite_rules');

add_filter('woocommerce_my_account_my_orders_actions', function ($actions, $order) {
    $user = wp_get_current_user();
    $allowed_roles = ['b2b_administrator', 'b2b_customer', 'b2b_engineer', 'b2b_viewer'];
    if (array_intersect($allowed_roles, $user->roles)) {
        $actions['view'] = [
            'url' => wc_get_endpoint_url('view-order', $order->get_id()),
            'name' => __('View', 'txtdomain')
        ];
    }
    return $actions;
}, 10, 2);

function lf_view_order_endpoint_content()
{
    $order_id = get_query_var('view-order');
    wc_get_template('myaccount/view-order.php', [
        'order_id' => $order_id,
        'is_customize' => true
    ]);
}
add_action('woocommerce_account_view-order_endpoint', 'lf_view_order_endpoint_content');

function hide_checkout_icon_for_non_b2b_users()
{
    if (is_user_logged_in()) {
        $user = wp_get_current_user();
        if (!in_array('b2b_administrator', $user->roles) && !in_array('b2b_customer', $user->roles)) {
            ?>
            <style>
                .widget_shopping_cart_content {
                    display: none !important;
                }

                .single-shop-item.flex.aic {
                    pointer-events: none !important;
                    opacity: 0.5;
                }

                .custom-button {
                    pointer-events: none !important;
                    opacity: 0.5;
                }

                #products-table_info {
                    pointer-events: none !important;
                    opacity: 0.5;
                }
            </style>
            <?php
        }
    }
}
add_action('wp_head', 'hide_checkout_icon_for_non_b2b_users');

function hide_billing_button()
{
    ?>
    <style>
        #wcmca_add_new_address_button_billing {
            display: none !important;
        }
    </style>
    <?php
}
add_filter('woocommerce_account_edit-address_endpoint', 'hide_billing_button', 10, 2);

add_action('wp_ajax_upload_additional_file', 'upload_additional_file');
function upload_additional_file()
{
    $_SESSION['upload_started'] = "true";
    $_SESSION['global_uploaded_file_name'] = "default";
    if (isset($_FILES['additional_files'])) {
        $file = $_FILES['additional_files'];
        $upload = wp_upload_bits($file['name'], null, file_get_contents($file['tmp_name']));

        if (!$upload['error']) {
            $_SESSION['global_uploaded_file_name'] = $upload['url'];
        }
    }
    my_plugin_custom_log("============= Uploaded file name ===============:" . $_SESSION['global_uploaded_file_name']);
    $_SESSION['upload_started'] = "false";
    wp_die(); // Always use wp_die() after an AJAX function to end execution.
}

add_filter('woocommerce_email_attachments', 'attach_csv_to_completed_order_email', 10, 4);

function attach_csv_to_completed_order_email($attachments, $email_id, $order, $email)
{
    if ($email_id === 'new_order' && is_a($order, 'WC_Order') && is_a($email, 'WC_Email_New_Order')) {
        $order_id = $order->get_id(); // Get the order ID
        $csv_file = generate_order_csv($order_id);

        $max_attempts = 100; // Maximum number of polling attempts
        $attempt = 0;
        $upload_status = null;

        // while ($attempt < $max_attempts) {
        //     if (isset($_SESSION['upload_started']) && $_SESSION['upload_started'] == "false") {
        //         break;
        //     }
        //     $attempt++;
        //     sleep(1);
        // }
        // my_plugin_custom_log("============= SESSION STATUS ===============:" . $_SESSION['upload_started']);
        // my_plugin_custom_log("============= attached file name ===============:" . $_SESSION['global_uploaded_file_name']);
        if ($csv_file && file_exists($csv_file)) {
            $attachments[] = $csv_file;
        }
        // if($_SESSION['global_uploaded_file_name'] != "default") {
        //     $attachments[] = $_SESSION['global_uploaded_file_name'];
        //     $_SESSION['global_uploaded_file_name'] = "default";
        // }
        // $additional_file = get_post_meta($order_id, '_additional_files', true);
    }
    return $attachments;
}

function generate_order_csv($order_id)
{
    $order = wc_get_order($order_id);
    if (!$order) {
        error_log("Order not found for ID: $order_id");
        return false;
    }
    $purchase_number = get_post_meta($order_id,"_purchase_order_number", true);
    $upload_dir = wp_upload_dir();
    $file_path = $upload_dir['basedir'] . "/order-report-{$purchase_number}.csv";
    $file = fopen($file_path, 'w');
    if (!$file) {
        error_log("Failed to open file for writing: $file_path");
        return false;
    }
    $header = ['Product Name', 'SKU', 'Quantity', 'Price', 'Total'];
    fputcsv($file, $header);
    foreach ($order->get_items() as $item) {
        $product = $item->get_product();
        $data = [
            $item->get_name(), // Product name
            $product ? $product->get_sku() : '', // SKU
            $item->get_quantity(), // Quantity
            $item->get_total() / $item->get_quantity(), // Price
            $item->get_total() // Total
        ];
        fputcsv($file, $data);
    }
    fclose($file);
    if (!file_exists($file_path)) {
        error_log("File not created successfully: $file_path");
        return false;
    }
    return $file_path;
}

function adding_checkout_addresses($current_addresses_list, $new_addresss_list, $customer_id)
{
    $tmp_new_address = [];
    foreach ($new_addresss_list as $new_address) {
        $tmp_new_address = $new_address;
        if (!isset($new_addresss_list["user_id"])) {
            $tmp_new_address["user_id"] = $customer_id;
        }
        $current_addresses_list[] = $tmp_new_address;
    }
    return $current_addresses_list;
}
function get_sub_user_ids($current_user_id)
{
    $main_user_id = get_main_b2b_admin_id($current_user_id);
    $args = array(
        'meta_key' => '_parent_admin_id',
        'meta_value' => $main_user_id,
        'meta_compare' => '=',
        'fields' => 'ID'
    );
    $user_query = new WP_User_Query($args);
    if (!empty($user_query->get_results())) {
        return $user_query->get_results();
    } else {
        return [];
    }
}

function add_custom_user_meta_column($columns)
{
    if (current_user_can('administrator')) {
        $email_column = $columns['email'];
        $columns['customer_meta'] = __('Customer #', 'woocommerce');
        $columns = array_merge(
            array_slice($columns, 0, array_search('email', array_keys($columns)) + 1),
            ['customer_meta' => __('Customer Meta', 'woocommerce')],
            array_slice($columns, array_search('email', array_keys($columns)) + 1)
        );
    }
    return $columns;
}
add_filter('manage_users_columns', 'add_custom_user_meta_column');

function display_customer_meta_value_in_column($value, $column_name, $user_id)
{
    if (current_user_can('administrator')) {
        if ('customer_meta' === $column_name) {
            $main_user_id = get_main_b2b_admin_id($user_id);
            $customer_meta = get_user_meta($main_user_id, '_customer', true);
            if (empty($customer_meta)) {
                return __('No Customer Meta', 'woocommerce');
            }
            return esc_html($customer_meta);
        }
    }

    return $value;
}
add_filter('manage_users_custom_column', 'display_customer_meta_value_in_column', 10, 3);

function custom_user_search_meta_field($query)
{
    if (is_admin() && isset($query->query_vars['search']) && !empty($query->query_vars['search'])) {
        global $wpdb;
        $search_term = $query->query_vars['search'];
        $search_term = trim($search_term, '*');

        if (!empty($search_term) && preg_match('/^\d+$/', $search_term)) {
            $query->set('meta_query', array(
                array(
                    'key' => '_customer',
                    'value' => $search_term,
                    'compare' => 'LIKE'
                )
            ));
            error_log('Modified query meta_query: ' . print_r($query->get('meta_query'), true));
        }
    }
}
add_action('pre_get_users', 'custom_user_search_meta_field');

add_action(
    'wp_login',
    function () {
        $current_user = wp_get_current_user();
        $main_user_id = get_main_b2b_admin_id($current_user->ID);
        $company_code = get_user_meta($main_user_id, '_companycode', true);
        if ($company_code == "3090") {
            $_SESSION['stock_suffix'] = 'us';
        } else {
            $_SESSION['stock_suffix'] = 'eu';
        }
    },
    10,
    2
);

function get_stock_suffix()
{
    $suffix = "";
    $current_user = wp_get_current_user();
    $main_user_id = get_main_b2b_admin_id($current_user->ID);
    $company_code = get_user_meta($main_user_id, '_companycode', true);
    if ($company_code == "3090") {
        $suffix = 'us';
    } else {
        $suffix = 'eu';
    }
    return $suffix;
}

add_filter('woocommerce_product_is_in_stock', function ($is_in_stock, $product) {
    error_log("===woocommerce_product_is_in_stock===");
    $suffix = get_stock_suffix();
    $meta_key = '_stock_' . $suffix;
    $stock = (int) get_post_meta($product->get_id(), $meta_key, true);
    return $stock > 0;
}, 10, 2);

// Getting stock quantity and availability based on suffix
add_filter('woocommerce_product_get_stock_quantity', function ($stock_quantity, $product) {
    if (!$product instanceof WC_Product) {
        return $stock_quantity;
    }
    error_log("===woocommerce_product_get_stock_quantity===");
    $suffix = get_stock_suffix();
    $meta_key = '_stock_' . $suffix;
    $custom_stock_quantity = get_post_meta($product->get_id(), $meta_key, true);
    if ($custom_stock_quantity !== '') {
        return intval($custom_stock_quantity);
    } else {
        return 0;
    }
}, 10, 2);

add_filter('woocommerce_get_availability', function ($availability, $product) {
    $suffix = get_stock_suffix();
    if ($suffix !== 'default') {
        $stock_quantity = get_post_meta($product->get_id(), "_stock_{$suffix}", true);
        if ((int) $stock_quantity <= 0) {
            $availability['availability'] = __('Out of stock', 'woocommerce');
            $availability['class'] = 'out-of-stock';
        }
    }

    return $availability;
}, 10, 2);

// Valide stock during add to cart
add_filter('woocommerce_add_to_cart_validation', 'custom_stock_us_validation', 10, 5);
function custom_stock_us_validation($passed, $product_id, $quantity, $variation_id = 0, $cart_item_data = [])
{
    my_plugin_custom_log("passed: $passed");
    $custom_suffix = get_stock_suffix();
    $manage_stock = get_post_meta($product_id, '_manage_stock_' . $custom_suffix, true);
    $stock_quantity = (int) get_post_meta($product_id, '_stock_' . $custom_suffix, true);
    $backorders = get_post_meta($product_id, '_backorders_' . $custom_suffix, true);

    my_plugin_custom_log("== ADD TO CAT VAL == manage stock : $manage_stock, stock quantity : $stock_quantity, Back-orders: $backorders, Requested Quantity: $quantity");
    if ($manage_stock === 'yes') {
        // If the product is out of stock and backorders are not allowed
        if ($stock_quantity < $quantity && $backorders !== 'yes') {
            wc_add_notice(__('Sorry, this product is not available in the requested quantity.', 'woocommerce'), 'error');
            return false;
        }
        else{
            return true;
        }
    }
    return $passed;

}

// add_action('woocommerce_checkout_process', function () {
//     $suffix = get_stock_suffix();
//     $cart = WC()->cart->get_cart();

//     foreach ($cart as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $quantity = $cart_item['quantity'];
//         $stock_quantity = (int) get_post_meta($product_id, "_stock_" . $suffix, true);

//         if ($quantity > $stock_quantity) {
//             wc_add_notice(
//                 sprintf(
//                     __('Not enough stock for "%s" in your region. Available: %d.', 'woocommerce'),
//                     $cart_item['data']->get_name(),
//                     $stock_quantity
//                 ),
//                 'error'
//             );
//         }
//     }
// });

add_filter('woocommerce_prevent_admin_stock_reduction', function ($prevent, $order) {
    $suffix = get_stock_suffix();
    $meta_field = "_stock_" . $suffix;
    foreach ($order->get_items() as $item) {
        $product_id = $item->get_product_id();
        $custom_stock = get_post_meta($product_id, $meta_field, true);
        $quantity = $item->get_quantity();

        if ($quantity > $custom_stock) {
            return true;
        } else {
            false;
        }
    }
}, 10, 2);

add_filter('woocommerce_cart_product_cannot_be_purchased_message', function ($message, $product_id) {
    return ''; // Return an empty message to bypass stock errors
}, 10, 2);

// Display available stock in cart and checkout region-based
add_filter('woocommerce_get_item_data', function ($item_data, $cart_item) {
    $suffix = get_stock_suffix();
    if ($suffix !== 'default') {
        $product_id = $cart_item['product_id'];
        $stock_quantity = get_post_meta($product_id, "_stock_{$suffix}", true);
        $item_data[] = array(
            'key' => __('Available Stock', 'woocommerce'),
            'value' => $stock_quantity,
        );
    }

    return $item_data;
}, 10, 2);

// Inject SAP Net Price and Sales Tax per cart line (TEMPORARILY DISABLED)
/*add_filter('woocommerce_get_item_data', function ($item_data, $cart_item) {
    try {
        if ( ! is_user_logged_in() ) {
            return $item_data;
        }
        $product = isset($cart_item['data']) ? $cart_item['data'] : null;
        if ( ! $product || ! ($product instanceof WC_Product) ) {
            return $item_data;
        }
        if ( ! function_exists('hytec_build_sales_order_payload') || ! function_exists('hytec_call_sap_sales_order') ) {
            if ( function_exists('hytec_sap_console_log') ) {
                hytec_sap_console_log('warn', 'SAP cart pricing: required functions missing');
            }
            return $item_data;
        }

        $user_id = get_current_user_id();
        $qty     = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

        if ( function_exists('hytec_sap_console_log') ) {
            hytec_sap_console_log('info', 'SAP cart pricing: building payload', array(
                'product_id' => $product->get_id(),
                'sku'        => $product->get_sku(),
                'qty'        => $qty,
                'user_id'    => $user_id,
            ));
        }

        // Simple per-request cache to avoid duplicate calls for same product+qty
        static $cache = array();
        $cache_key = $product->get_id() . '|' . $qty;
        $net_value = '';
        $sales_tax = '';

        if ( isset( $cache[$cache_key] ) ) {
            $net_value = $cache[$cache_key]['net_value'];
            $sales_tax = $cache[$cache_key]['sales_tax'];
        } else {
            $payload  = hytec_build_sales_order_payload( $product, $user_id, $qty );
            if ( ! $payload ) {
                if ( function_exists('hytec_sap_console_log') ) {
                    hytec_sap_console_log('warn', 'SAP cart pricing: no payload (likely missing Sold-To)');
                }
                return $item_data;
            }
            $response = hytec_call_sap_sales_order( $payload );
            if ( is_wp_error( $response ) ) {
                if ( function_exists('hytec_sap_console_log') ) {
                    hytec_sap_console_log('error', 'SAP cart pricing: call failed', array(
                        'code' => $response->get_error_code(),
                        'msg'  => $response->get_error_message(),
                    ));
                }
                return $item_data; // fail silent on cart for UX
            }

            // store a copy for debug UI in footer
            if ( ! isset($GLOBALS['HYTEC_SAP_CART_DEBUG']) || ! is_array($GLOBALS['HYTEC_SAP_CART_DEBUG']) ) {
                $GLOBALS['HYTEC_SAP_CART_DEBUG'] = array();
            }
            $GLOBALS['HYTEC_SAP_CART_DEBUG'][] = array(
                'product_id' => $product->get_id(),
                'sku'        => $product->get_sku(),
                'qty'        => $qty,
                'response'   => $response,
            );

            $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
            $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
            $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
            $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
            $cache[$cache_key] = array('net_value'=>$net_value, 'sales_tax'=>$sales_tax);

            if ( function_exists('hytec_sap_console_log') ) {
                hytec_sap_console_log('info', 'SAP cart pricing: response OK', array(
                    'net_value' => $net_value,
                    'sales_tax' => $sales_tax,
                    'raw_first' => $first,
                ));
            }
        }

        if ( $net_value !== '' ) {
            // Convert SAP response (divide by 100) and format with currency symbol
            $net_value_decimal = is_numeric( $net_value ) ? (float)$net_value / 100 : 0;
            $formatted_net_price = is_numeric( $net_value ) ? get_woocommerce_currency_symbol() . number_format( $net_value_decimal, 2, '.', '' ) : wc_clean( (string) $net_value );
            $item_data[] = array(
                'key'   => __('Net Price', 'woocommerce'),
                'value' => wc_clean( (string) $net_value_decimal ),
                'display' => $formatted_net_price,
            );
        }
        if ( $sales_tax !== '' ) {
            // Convert SAP response (divide by 100) and format with currency symbol
            $sales_tax_decimal = is_numeric( $sales_tax ) ? (float)$sales_tax / 100 : 0;
            $formatted_sales_tax = is_numeric( $sales_tax ) ? get_woocommerce_currency_symbol() . number_format( $sales_tax_decimal, 2, '.', '' ) : wc_clean( (string) $sales_tax );
            $item_data[] = array(
                'key'   => __('Sales Tax', 'woocommerce'),
                'value' => wc_clean( (string) $sales_tax_decimal ),
                'display' => $formatted_sales_tax,
            );
        }

        // Optional: small hint for admins
        if ( function_exists('hytec_sap_debug_ui_enabled') && hytec_sap_debug_ui_enabled() && current_user_can('manage_options') ) {
            $item_data[] = array(
                'key'   => __('SAP Debug', 'woocommerce'),
                'value' => __('See browser console for SAP pricing logs', 'woocommerce'),
            );
        }

// Render SAP cart debug UI similar to product page
add_action('wp_footer', function() {
    if ( ! function_exists('is_cart') || ! is_cart() ) return;
    if ( ! function_exists('hytec_sap_debug_ui_enabled') || ! hytec_sap_debug_ui_enabled() ) return;

    // Only for logged-in users to avoid leaking details
    if ( ! is_user_logged_in() ) return;

    echo '<div class="sap-cart-debug" style="margin:12px 0;">';

    // Show what items are in cart and their quantities
    if ( function_exists('WC') && WC()->cart ) {
        echo '<details class="sap-cart-items"><summary>Cart items (debug)</summary><div style="font-family:monospace;font-size:12px;">';
        foreach ( WC()->cart->get_cart() as $cart_item_key => $ci ) {
            $p = isset($ci['data']) ? $ci['data'] : null;
            if ( $p instanceof WC_Product ) {
                echo '<div>Product #' . esc_html( (string) $p->get_id() ) . ' SKU ' . esc_html( $p->get_sku() ) . ' — Qty ' . esc_html( (string) intval($ci['quantity']) ) . '</div>';
            }
        }
        echo '</div></details>';
    }

    // Print last request snapshot (sanitized) if available (from last SAP call)
    if ( function_exists('hytec_sap_print_request_debug') ) {
        hytec_sap_print_request_debug();
    }

    // Print captured responses for this cart render
    if ( isset($GLOBALS['HYTEC_SAP_CART_DEBUG']) && is_array($GLOBALS['HYTEC_SAP_CART_DEBUG']) && ! empty($GLOBALS['HYTEC_SAP_CART_DEBUG']) ) {
        foreach ( $GLOBALS['HYTEC_SAP_CART_DEBUG'] as $k => $entry ) {
            $pid = isset($entry['product_id']) ? intval($entry['product_id']) : 0;
            $sku = isset($entry['sku']) ? (string) $entry['sku'] : '';
            $qty = isset($entry['qty']) ? intval($entry['qty']) : 0;
            $res = isset($entry['response']) ? $entry['response'] : array();
            echo '<details class="sap-response-debug" style="margin-top:8px;">';
            echo '<summary>SAP API Response (Cart debug) — Product #' . esc_html( (string) $pid ) . ' SKU ' . esc_html( $sku ) . ' Qty ' . esc_html( (string) $qty ) . '</summary>';
            echo '<pre style="white-space:pre-wrap;max-height:400px;overflow:auto;">' . esc_html( print_r( $res, true ) ) . '</pre>';
            echo '</details>';
        }
    } else {
        echo '<div style="color:#888;font-size:12px;">No SAP cart responses captured in this render.</div>';
    }

    echo '</div>';
});

    } catch ( \Throwable $e ) {
        if ( function_exists('hytec_sap_console_log') ) {
            hytec_sap_console_log('error', 'SAP cart pricing exception', array('msg'=>$e->getMessage()));
        }
        // swallow errors in cart UI
    }
    return $item_data;
}, 20, 2);

// Alternative approach: Hook directly into cart item display
add_action('woocommerce_after_cart_item_name', function($cart_item, $cart_item_key) {
    try {
        if ( ! is_user_logged_in() ) {
            return;
        }

        $product = isset($cart_item['data']) ? $cart_item['data'] : null;
        if ( ! $product || ! ($product instanceof WC_Product) ) {
            return;
        }

        if ( ! function_exists('hytec_build_sales_order_payload') || ! function_exists('hytec_call_sap_sales_order') ) {
            return;
        }

        $user_id = get_current_user_id();
        $qty     = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

        // Simple per-request cache to avoid duplicate calls for same product+qty
        static $cache = array();
        $cache_key = $product->get_id() . '|' . $qty;
        $net_value = '';
        $sales_tax = '';

        if ( isset( $cache[$cache_key] ) ) {
            $net_value = $cache[$cache_key]['net_value'];
            $sales_tax = $cache[$cache_key]['sales_tax'];
        } else {
            $payload  = hytec_build_sales_order_payload( $product, $user_id, $qty );
            if ( ! $payload ) {
                return;
            }
            $response = hytec_call_sap_sales_order( $payload );
            if ( is_wp_error( $response ) ) {
                return;
            }

            $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
            $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
            $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
            $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
            $cache[$cache_key] = array('net_value'=>$net_value, 'sales_tax'=>$sales_tax);
        }

        // Display SAP pricing if we have data
        /*if ( $net_value !== '' || $sales_tax !== '' ) {
            echo '<div class="sap-cart-pricing" style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007cba;">';
            echo '<div class="sap-pricing-title" style="font-weight: 600; font-size: 12px; color: #555; margin-bottom: 4px;">SAP Pricing</div>';
            echo '<div class="sap-pricing-values" style="display: flex; gap: 15px; font-size: 13px;">';

            if ( $net_value !== '' ) {
                $formatted_net_price = is_numeric( $net_value ) ? wc_price( $net_value ) : esc_html( $net_value );
                echo '<div class="sap-pricing-item">';
                echo '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Net Price:</span>';
                echo '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' . $formatted_net_price . '</span>';
                echo '</div>';
            }

            if ( $sales_tax !== '' ) {
                $formatted_sales_tax = is_numeric( $sales_tax ) ? wc_price( $sales_tax ) : esc_html( $sales_tax );
                echo '<div class="sap-pricing-item">';
                echo '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Sales Tax:</span>';
                echo '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' . $formatted_sales_tax . '</span>';
                echo '</div>';
            }

            echo '</div>';
            echo '</div>';
        }

    } catch ( \Throwable $e ) {
        // Silently handle errors in cart display
    }
}, 10, 2);*/



add_action('woocommerce_checkout_create_order', 'update_custom_stock_on_order', 10, 2);

function update_custom_stock_on_order($order, $data)
{
    $suffix = get_stock_suffix();
    $order_id = $order->get_id();
    $items = $order->get_items();
    $order->set_currency(get_custom_currency());
    foreach ($items as $item) {
        $product_id = $item->get_product_id();
        $quantity = $item->get_quantity();
        $current_stock = (int) get_post_meta($product_id, '_stock_' . $suffix, true);
        if ($current_stock !== '') {
            // $new_stock = max(0, $current_stock - (int) $quantity); // Prevent negative stock
            $new_stock =  (int)$current_stock - (int) $quantity; // Prevent negative stock
            update_post_meta($product_id, '_stock_' . $suffix, $new_stock);
            my_plugin_custom_log(get_post_meta($product_id, '_stock_' . $suffix, true));
        }
    }
}

// Change woocommerce_currency
add_filter('woocommerce_currency', function ($currency) {
    $custom_currency = get_custom_currency();
    return $custom_currency;
});

// add_action('woocommerce_check_cart_items', function () {
//     $suffix = get_stock_suffix();
//     $meta_stock_key = "_stock_" . $suffix;

//     foreach (WC()->cart->get_cart() as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $quantity = $cart_item['quantity'];
//         $available_stock = (int) get_post_meta($product_id, $meta_stock_key, true);

//         if ($available_stock < $quantity) {
//             wc_add_notice(
//                 sprintf(
//                     'Not enough units of %s are available in stock to fulfill this order.',
//                     get_the_title($product_id)
//                 ),
//                 'error'
//             );
//         }
//     }
// });

// add_action('woocommerce_after_checkout_validation', function ($data, $errors) {
//     if (!empty($errors->get_error_messages())) {
//         return;
//     }
//     $suffix = get_stock_suffix();
//     $meta_stock_key = "_stock_" . $suffix;
//     foreach (WC()->cart->get_cart() as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $custom_stock = get_post_meta($product_id, $meta_stock_key, true); // Custom stock field
//         $quantity = $cart_item['quantity'];

//         if ($quantity > $custom_stock) {
//             $errors->add('stock_validation_error', __('Custom stock validation failed.', 'woocommerce'));
//         }
//     }
// }, 10, 2);


add_filter('woocommerce_get_availability_text', function ($availability, $product) {
    $suffix = get_stock_suffix();
    $meta_stock_key = '_stock_' . $suffix;
    $stock = (int) get_post_meta($product->get_id(), $meta_stock_key, true);
    if ($stock > 0) {
        return sprintf(__('In stock (%d units)', 'woocommerce'), $stock);
    } else {
        return __('Out of stock', 'woocommerce');
    }
}, 10, 2);

// add_action('woocommerce_checkout_process', function () {
//     foreach (WC()->cart->get_cart() as $cart_item) {
//         $product_id = $cart_item['product_id'];
//         $quantity = $cart_item['quantity'];
//         $suffix = get_stock_suffix();
//         $meta_stock_key = '_stock_' . $suffix;
//         $available_stock = (int) get_post_meta($product_id, $meta_stock_key, true);
//         if ($available_stock < $quantity) {
//             wc_add_notice(
//                 sprintf(
//                     'Not enough units of %s are available in stock to fulfill this order.',
//                     "woocommerce_checkout_process"
//                 ),
//                 'error'
//             );
//         }
//     }
// });

add_filter('woocommerce_product_get_stock_quantity', function ($stock, $product) {
    $suffix = get_stock_suffix(); // Your logic to determine the region (US/EU).
    $custom_stock_meta = '_stock_' . $suffix;
    // Fetch custom stock value
    $custom_stock = get_post_meta($product->get_id(), $custom_stock_meta, true);

    if ($custom_stock !== '') {
        return (int) $custom_stock; // Return custom stock.
    }
    return (int) $custom_stock; // Default fallback.
}, 10, 2);

// add_filter('woocommerce_product_is_in_stock', function ($is_in_stock, $product) {
//     $suffix = get_stock_suffix();
//     $custom_stock_meta = '_stock_' . $suffix;
//     $custom_stock = get_post_meta($product->get_id(), $custom_stock_meta, true);
//     if ($custom_stock <= 0) {
//         return false;
//     }
//     return true;
// }, 10, 2);

add_filter('manage_edit-shop_order_columns', 'add_custom_order_column');
function add_custom_order_column($columns)
{
    if (isset($columns['order_number'])) {
        unset($columns['order_number']);
    }
    if (isset($columns['order_total'])) {
        unset($columns['order_total']);
    }
    $new_column = array('custom_order_number' => __('Order', 'woocommerce'));
    $first_column = array_slice($columns, 0, 1, true);
    $rest_columns = array_slice($columns, 1, null, true);
    $columns = array_merge($first_column, $new_column, $rest_columns);
    $columns['custom_total'] = __('Total', 'woocommerce');
    return $columns;
}

// Step 2: Populate the custom column with your custom total
add_action('manage_shop_order_posts_custom_column', 'populate_custom_order_column', 10, 2);
function populate_custom_order_column($column, $post_id)
{
    $order = wc_get_order($post_id);
    if ($column === 'custom_order_number') {
        $purchase_order_id = get_post_meta($post_id, '_purchase_order_number', true);
        $customer_id = $order->get_customer_id();
        $customer_name = "";
        if ($customer_id) {
            $customer_user = get_userdata($customer_id);
            if ($customer_user) {
                $customer_name = ucfirst($customer_user->user_login);
            }
        }

        echo '<a href="#" class="order-preview" data-order-id="' . $post_id . '" title="Preview">Preview</a><a href="/wp-admin/post.php?post=' . $post_id . '&amp;action=edit" class="order-view"><strong>#' . $purchase_order_id . ' ' . $customer_name . '</strong></a>';
    }

    if ($column === 'custom_total') {
        $currency = get_post_meta($post_id, '_order_currency', true);
        if (!$currency) {
            $currency = $order->get_currency();
        }
        if ($currency == "USD") {
            $currency_symbol = "$";
        } else if ($currency == "EUR") {
            $currency_symbol = "€";
        } else {
            $currency_symbol = "£";
        }
        $order_total = $order->get_total();
        echo $currency_symbol . ' ' . $order_total;
    }
}

add_filter('manage_edit-shop_order_sortable_columns', function ($sortable_columns) {
    $sortable_columns['custom_order_number'] = '_purchase_order_number';
    return $sortable_columns;
});

// Handle sorting query
add_action('pre_get_posts', function ($query) {
    if (!is_admin())
        return;

    $orderby = $query->get('orderby');
    if ('custom_order_number' === $orderby) {
        $query->set('meta_key', '_purchase_order_number');
        $query->set('orderby', 'meta_value_num');
    }
});

add_action('woocommerce_admin_order_data_after_order_details', 'set_custom_currency_script');
function set_custom_currency_script($order)
{
    $order_id = $order->get_id();
    $order_currency = get_post_meta($order_id, '_order_currency', true);
    if (!$order_currency) {
        $order_currency = $order->get_currency();
    }

    $currency_symbol = "";
    if ($order_currency == "USD") {
        $currency_symbol = "$";
    } else if ($order_currency == "EUR") {
        $currency_symbol = "€";
    } else {
        $currency_symbol = "£";
    }

    $purchase_order_number = get_post_meta($order_id, '_purchase_order_number', true);
    $purchase_order_number = "Order #" . $purchase_order_number . " details";
    ?>
    <script>
        jQuery(document).ready(function ($) {
            $('.woocommerce-Price-currencySymbol').text('<?php echo esc_js($currency_symbol); ?>');
            $('.woocommerce-order-data__heading').text('<?php echo esc_js($purchase_order_number); ?>');
        });
    </script>
    <?php
}


add_action('woocommerce_email_header', 'custom_email_header_content', 10, 2);
function custom_email_header_content($email_heading, $email)
{
    // Check if the email object exists and contains an order
    if (isset($email->object) && is_a($email->object, 'WC_Order')) {
        $order = $email->object; // Get the order object
        $p_n = get_post_meta($order->get_id(), '_purchase_order_number', true);
        $order_date = wc_format_datetime($order->get_date_created());
        ?>
        <body <?php echo is_rtl() ? 'rightmargin' : 'leftmargin'; ?>="0" marginwidth="0" topmargin="0" marginheight="0"
            offset="0">
            <table width="100%" id="outer_wrapper">
                <tr>
                    <td><!-- Deliberately empty to support consistent sizing and layout across multiple email clients. --></td>
                    <td width="600">
                        <div id="wrapper" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
                            <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%">
                                <tr>
                                    <td align="center" valign="top">
                                        <div id="template_header_image">
                                            <?php
                                            $img = get_option('woocommerce_email_header_image');

                                            if ($img) {
                                                echo '<p style="margin-top:0;"><img src="' . esc_url($img) . '" alt="' . esc_attr(get_bloginfo('name', 'display')) . '" /></p>';
                                            }
                                            ?>
                                        </div>
                                        <table border="0" cellpadding="0" cellspacing="0" width="100%" id="template_container">
                                            <tr>
                                                <td align="center" valign="top">
                                                    <!-- Header -->
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        id="template_header">
                                                        <tr>
                                                            <td id="header_wrapper">
                                                                <h1><?php echo "New Order: #" . $p_n; ?></h1>
                                                            </td>
                                                        </tr>
                                                    </table>
                                                    <!-- End Header -->
                                                </td>
                                            </tr>
                                            <tr>
                                                <td align="center" valign="top">
                                                    <!-- Body -->
                                                    <table border="0" cellpadding="0" cellspacing="0" width="100%"
                                                        id="template_body">
                                                        <tr>
                                                            <td valign="top" id="body_content">
                                                                <!-- Content -->
                                                                <table border="0" cellpadding="20" cellspacing="0" width="100%">
                                                                    <tr>
                                                                        <td valign="top">
                                                                            <div id="body_content_inner"></div>
                                                                            <?php
    }
}

add_filter( 'woocommerce_email_subject_new_order', 'custom_new_order_email_subject', 10, 2 );
function custom_new_order_email_subject( $subject, $order ) {
    $p_n = get_post_meta($order->get_id(), '_purchase_order_number', true);
    $subject = sprintf( 'New Order #%s', $p_n );

    return $subject;
}

add_filter('woocommerce_email_recipient_new_order', 'route_order_emails_by_country', 10, 2);
function route_order_emails_by_country($recipient, $order) {
    if (!($order instanceof WC_Order)) {
        return $recipient;
    }

    $customer_id = $order->get_customer_id();
    $main_admin_id = get_main_b2b_admin_id($customer_id);

    $company_code = get_user_meta($main_admin_id, '_companycode', true);

    $us_email = '<EMAIL>';
    // $eu_gb_email = '<EMAIL>';
    $eu_gb_email = '<EMAIL>';

    // $us_email = '<EMAIL>';
    // $eu_gb_email = '<EMAIL>';

    my_plugin_custom_log($company_code);
    if ($company_code === '3090') {
        $recipient = $us_email;
    } else{
        $recipient = $eu_gb_email;
    }

    return $recipient;
}

function my_plugin_custom_log($message) {
    $log_file = plugin_dir_path(__FILE__) . 'my-plugin-log.txt'; // Specify file location
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}


add_filter('auth_cookie_expiration', 'custom_session_expiration_time', 99, 3);
function custom_session_expiration_time($expiration, $user_id, $remember) {
    return 86400;
}

// add_action('wp_login', 'check_password_expiration', 10, 2);

// function check_password_expiration($user_login, $user) {
//     $password_changed_date = get_user_meta($user->ID, 'password_changed_date', true);
//     if (!$password_changed_date) {
//         $password_changed_date = current_time('timestamp');
//         update_user_meta($user->ID, 'password_changed_date', $password_changed_date);
//     }

//     $days_since_change = (current_time('timestamp') - $password_changed_date) / DAY_IN_SECONDS;

//     if ($days_since_change > 90) {
//         wp_logout();
//         wp_die('Your password has expired. Please reset your password to continue.');
//     }
// }

add_action('woocommerce_add_to_cart', 'debug_add_to_cart', 10, 6);

function debug_add_to_cart($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data)
{
    my_plugin_custom_log("Add to cart debug: Product ID: $product_id, Quantity: $quantity");
}

add_filter('woocommerce_add_cart_item_data', 'custom_add_cart_item_data', 10, 2);

function custom_add_cart_item_data($cart_item_data, $product_id) {
    $custom_suffix = get_stock_suffix();
    $manage_stock = get_post_meta($product_id, '_manage_stock_' . $custom_suffix, true);
    $stock_quantity = (int) get_post_meta($product_id, '_stock_' . $custom_suffix, true);
    $backorders = get_post_meta($product_id, '_backorders_' . $custom_suffix, true);
    if ($manage_stock === 'yes') {
        // Custom backorder logic
        if ($stock_quantity <= 0 && $backorders === 'yes') {
            $cart_item_data['is_backorder'] = true; // Custom marker for backorder items
        } elseif ($stock_quantity < $cart_item_data['quantity']) {
            return false;
        }
    }
    return $cart_item_data;
}
add_filter('woocommerce_cart_item_is_backorder', 'custom_cart_item_backorder', 10, 2);

function custom_cart_item_backorder($is_backorder, $cart_item) {
    if (isset($cart_item['is_backorder']) && $cart_item['is_backorder'] === true) {
        return true;
    }
    return $is_backorder;
}
add_action('admin_init', function () {
    remove_action('admin_color_scheme_picker', 'admin_color_scheme_picker');
});
add_action('admin_head', function () {
    // Hide the fields with CSS
    echo '<style>
        tr.user-rich-editing-wrap,
        tr.user-comment-shortcuts-wrap,
        tr.show-admin-bar,
        tr.user-language-wrap {
            display: none !important;
        }
		#tax_exempt_options,
        .wc-customer-tax-exempt,
        .woocommerce-tax-exempt-wrap {
            display: none !important;
        }
    </style>';
});

// Optional: remove the language dropdown server-side
add_filter('get_user_option_locale', function($locale, $user_id) {
    return get_locale(); // Forces the site locale
}, 10, 2);
add_action('admin_init', function () {
    // Only run on user-new.php and when redirected after creating a user
    if (is_admin() && isset($_GET['update']) && $_GET['update'] === 'add' && current_user_can('edit_users')) {
        // Get the latest user created by ID (may be rough, but WordPress doesn’t pass ID in URL)
        $recent_user = get_users([
            'number' => 1,
            'orderby' => 'ID',
            'order' => 'DESC',
            'fields' => ['ID']
        ]);

        if (!empty($recent_user)) {
            $user_id = $recent_user[0]->ID;
            wp_redirect(admin_url("user-edit.php?user_id={$user_id}"));
            exit;
        }
    }
});

// remove_filter( 'wp_authenticate_user', 'wp_check_password_reset_key_expired' );

// Billing and shipping addresses fields
add_filter( 'woocommerce_default_address_fields' , 'filter_default_address_fields', 20, 1 );
function filter_default_address_fields( $address_fields ) {
    // Only on checkout page
    if( ! is_checkout() ) return $address_fields;

    // All field keys in this array
    $key_fields = array('company');

    // Loop through each address fields (billing and shipping)
    foreach( $key_fields as $key_field )
        $address_fields[$key_field]['required'] = false;

    return $address_fields;
}
// Removed: Phone field function - not needed since we don't have shipping phone field
// add_filter( 'woocommerce_checkout_fields', 'your_require_wc_phone_field');
// function your_require_wc_phone_field( $fields ) {
//     if (isset($fields['shipping']['shipping_phone'])) {
//         $fields['shipping']['shipping_phone']['required'] = false;
//     }
//     return $fields;
// }



add_filter( 'woocommerce_checkout_fields' , 'custom_remove_billing_phone_field' );

function custom_remove_billing_phone_field( $fields ) {
    unset($fields['billing']['billing_phone']);
    return $fields;
}

// Add Export Compliance checkbox based on selected shipping country
add_action('woocommerce_review_order_before_submit', 'add_export_compliance_checkbox');
function add_export_compliance_checkbox() {
    ?>
    <div id="export_compliance_container" class="form-row terms wc-terms-and-conditions" style="margin-bottom:1em; display:none;">
        <p style="margin-bottom: 1em;">
            Purchasing party shall comply with all export laws and regulations of the United States and other applicable jurisdictions.
            Purchasing party represents that it is not named on any U.S. government list of persons or entities prohibited from receiving exports,
            and you shall not permit products to be purchased in violation of any U.S. export embargo, prohibition or restriction.
        </p>
        <p class="form-row validate-required" style="margin-bottom:0;">
            <label class="woocommerce-form__label woocommerce-form__label-for-checkbox checkbox">
                <input type="checkbox" class="woocommerce-form__input woocommerce-form__input-checkbox input-checkbox" id="export_compliance" name="export_compliance" />
                <span class="woocommerce-form__label-text">I agree with the Export Compliance</span>&nbsp;<abbr class="required" title="required">*</abbr>
            </label>
        </p>
    </div>
    <script>
    jQuery(function($){
        function toggleExportComplianceBox(){
            var country = $('#shipping_country').val() || '';
            var isUS = (country.toUpperCase() === 'US');
            var $box = $('#export_compliance_container');
            if (isUS) {
                $box.hide();
                $('#export_compliance').prop('checked', false);
            } else {
                $box.show();
            }
        }
        // Initial check and on updates
        toggleExportComplianceBox();
        $(document.body).on('change', '#shipping_country', toggleExportComplianceBox);
        $(document.body).on('country_to_state_changed updated_checkout', toggleExportComplianceBox);
    });
    </script>
    <?php
}

// Save export compliance checkbox value
add_action('woocommerce_checkout_update_order_meta', 'save_export_compliance_checkbox');
function save_export_compliance_checkbox($order_id) {
    $shipping_country = isset($_POST['shipping_country']) ? wc_clean( wp_unslash( $_POST['shipping_country'] ) ) : '';
    $is_us_shipping = ( strtoupper( (string) $shipping_country ) === 'US' );

    // Only process for non-US shipping destinations
    if ( ! $is_us_shipping ) {
        if ( isset($_POST['export_compliance']) && $_POST['export_compliance'] ) {
            update_post_meta($order_id, '_export_compliance_agreed', 'yes');
            update_post_meta($order_id, '_export_compliance_date', current_time('mysql'));
        }
    } else {
        // Ensure not set for US shipments
        delete_post_meta($order_id, '_export_compliance_agreed');
        delete_post_meta($order_id, '_export_compliance_date');
    }
}

// Validate export compliance on checkout
add_action('woocommerce_checkout_process', 'validate_export_compliance_checkbox');
function validate_export_compliance_checkbox() {
    $shipping_country = isset($_POST['shipping_country']) ? wc_clean( wp_unslash( $_POST['shipping_country'] ) ) : '';
    $is_us_shipping = ( strtoupper( (string) $shipping_country ) === 'US' );

    // Only validate for non-US shipping destinations
    if ( ! $is_us_shipping ) {
        if ( ! isset($_POST['export_compliance']) || ! $_POST['export_compliance'] ) {
            wc_add_notice('Please read and accept the Export Compliance agreement to proceed.', 'error');
        }
    }
}

// Validate Shipping Method Preference and Shipping Freight Terms dropdowns
add_action('woocommerce_checkout_process', 'validate_shipping_dropdown_fields');
function validate_shipping_dropdown_fields() {
    // Validate Shipping Method Preference
    if (!isset($_POST['shipping_method_preference']) || empty($_POST['shipping_method_preference'])) {
        wc_add_notice('Shipping Method Preference is a required field.', 'error');
    }

    // Validate Shipping Freight Terms (shipping_account_number field)
    if (!isset($_POST['shipping_account_number']) || empty($_POST['shipping_account_number'])) {
        wc_add_notice('Shipping Freight Terms is a required field.', 'error');
    }
}

// Add SAP pricing to cart page via JavaScript injection for WooCommerce Blocks
add_action('wp_footer', function() {
    if ( ! is_cart() || ! is_user_logged_in() ) {
        return;
    }

    // Get SAP pricing data for all cart items
    $sap_pricing_data = array();

    if ( WC()->cart && ! WC()->cart->is_empty() ) {
        foreach ( WC()->cart->get_cart() as $cart_item_key => $cart_item ) {
            $product = isset($cart_item['data']) ? $cart_item['data'] : null;
            if ( ! $product || ! ($product instanceof WC_Product) ) {
                continue;
            }

            if ( ! function_exists('hytec_build_sales_order_payload') || ! function_exists('hytec_call_sap_sales_order') ) {
                continue;
            }

            $user_id = get_current_user_id();
            $qty     = isset($cart_item['quantity']) ? max(1, intval($cart_item['quantity'])) : 1;

            // Simple per-request cache to avoid duplicate calls for same product+qty
            static $cache = array();
            $cache_key = $product->get_id() . '|' . $qty;
            $net_value = '';
            $sales_tax = '';

            if ( isset( $cache[$cache_key] ) ) {
                $net_value = $cache[$cache_key]['net_value'];
                $sales_tax = $cache[$cache_key]['sales_tax'];
            } else {
                $payload  = hytec_build_sales_order_payload( $product, $user_id, $qty );
                if ( ! $payload ) {
                    continue;
                }
                $response = hytec_call_sap_sales_order( $payload );
                if ( is_wp_error( $response ) ) {
                    continue;
                }

                $line_items = isset( $response['line_items'] ) && is_array( $response['line_items'] ) ? $response['line_items'] : array();
                $first      = isset( $line_items[0] ) && is_array( $line_items[0] ) ? $line_items[0] : array();
                $net_value  = isset( $first['net_value'] ) ? $first['net_value'] : '';
                $sales_tax  = isset( $first['sales_tax'] ) ? $first['sales_tax'] : '';
                $cache[$cache_key] = array('net_value'=>$net_value, 'sales_tax'=>$sales_tax);
            }

            // Add SAP pricing to data array
            if ( $net_value !== '' || $sales_tax !== '' ) {
                $sap_pricing_data[$product->get_sku()] = array(
                    'net_value' => $net_value,
                    'sales_tax' => $sales_tax,
                    'formatted_net_value' => is_numeric( $net_value ) ? wc_price( $net_value ) : $net_value,
                    'formatted_sales_tax' => is_numeric( $sales_tax ) ? wc_price( $sales_tax ) : $sales_tax,
                );
            }
        }
    }

    if ( ! empty( $sap_pricing_data ) ) {
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            var sapPricingData = <?php echo wp_json_encode( $sap_pricing_data ); ?>;

            function addSapPricing() {
                // Find all actual cart items (not totals or other price elements)
                $('.wc-block-cart-item').each(function(index) {
                    var $cartItem = $(this);

                    // Skip if SAP pricing already added to this cart item
                    if ($cartItem.find('.sap-cart-pricing').length > 0) {
                        return;
                    }

                    // Find the price element within this specific cart item
                    var $priceElement = $cartItem.find('.wc-block-formatted-money-amount.wc-block-components-formatted-money-amount.wc-block-components-product-price__value').first();

                    if ($priceElement.length === 0) {
                        return; // No price element found in this cart item
                    }

                    // Try to find product SKU from the cart item
                    var productSku = null;

                    // Method 1: Look for SKU in the product details text
                    var $cartItemText = $cartItem.text();
                    console.log('Cart item ' + index + ' text:', $cartItemText);
                    console.log('Available SAP SKUs:', Object.keys(sapPricingData));

                    for (var sku in sapPricingData) {
                        if ($cartItemText.indexOf(sku) !== -1) {
                            productSku = sku;
                            console.log('Matched SKU:', sku, 'for cart item', index);
                            break;
                        }
                    }

                    // Method 2: If no SKU found, try to match by index (fallback)
                    if (!productSku) {
                        var skuKeys = Object.keys(sapPricingData);
                        if (skuKeys[index]) {
                            productSku = skuKeys[index];
                            console.log('Fallback matched SKU:', productSku, 'for cart item', index);
                        }
                    }

                    // If we found a matching SKU, add the SAP pricing
                    if (productSku && sapPricingData[productSku]) {
                        var sapData = sapPricingData[productSku];

                        var sapHtml = '<div class="sap-cart-pricing" style="margin: 8px 0; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007cba;">' +
                            '<div class="sap-pricing-title" style="font-weight: 600; font-size: 12px; color: #555; margin-bottom: 4px;">SAP Pricing</div>' +
                            '<div class="sap-pricing-values" style="display: flex; gap: 15px; font-size: 13px;">';

                        if (sapData.formatted_net_value) {
                            sapHtml += '<div class="sap-pricing-item">' +
                                '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Net Price:</span>' +
                                '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' + sapData.formatted_net_value + '</span>' +
                                '</div>';
                        }

                        if (sapData.formatted_sales_tax) {
                            sapHtml += '<div class="sap-pricing-item">' +
                                '<span class="sap-pricing-label" style="color: #666; font-weight: 500;">Sales Tax:</span>' +
                                '<span class="sap-pricing-value" style="color: #333; font-weight: 600; margin-left: 4px;">' + sapData.formatted_sales_tax + '</span>' +
                                '</div>';
                        }

                        sapHtml += '</div></div>';

                        // Insert SAP pricing after the price element
                        $priceElement.after(sapHtml);
                    }
                });
            }

            // Add SAP pricing on page load
            addSapPricing();

            // Re-add SAP pricing when cart updates (for AJAX updates)
            $(document.body).on('updated_cart_totals updated_checkout', function() {
                setTimeout(addSapPricing, 500);
            });

            // Also try with a slight delay for blocks that load asynchronously
            setTimeout(addSapPricing, 1000);
        });
        </script>
        <?php
    }
});