<?php
/**
 * Plugin Name: SAP ShipTo Addresses API
 * Description: Processes SAP shipping addresses and stores them in WCMCA compatible format
 * Version: 1.0
 * Author: ATAK Interactive
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Register REST route
add_action( 'rest_api_init', function() {
    register_rest_route( 'wc/v3', '/sap-shipto', [
        'methods'             => 'POST',
        'callback'            => 'sap_process_shipto_addresses',
        'permission_callback' => 'sap_shipto_permission_check',
    ] );

    // Test endpoint (keep open for testing)
    register_rest_route( 'wc/v3', '/sap-shipto-test', [
        'methods'             => 'GET',
        'callback'            => function() {
            return rest_ensure_response([
                'status' => 'working',
                'message' => 'SAP ShipTo Addresses API is active',
                'timestamp' => current_time( 'mysql' ),
                'auth_required' => 'yes'
            ]);
        },
        'permission_callback' => '__return_true',
    ] );

    // Debug endpoint to inspect JWT token contents
    register_rest_route( 'wc/v3', '/sap-shipto-debug-token', [
        'methods'             => 'POST',
        'callback'            => function( WP_REST_Request $request ) {
            $auth_header = $request->get_header( 'authorization' );
            if ( ! $auth_header || ! preg_match( '/Bearer\s+(.*)$/i', $auth_header, $matches ) ) {
                return new WP_Error( 'no_token', 'No token provided', [ 'status' => 400 ] );
            }

            $token = $matches[1];
            $parts = explode( '.', $token );

            if ( count( $parts ) !== 3 ) {
                return new WP_Error( 'invalid_token', 'Invalid token format', [ 'status' => 400 ] );
            }

            // Decode the payload (without verification for debugging)
            $payload = json_decode( base64_decode( $parts[1] ), true );

            return rest_ensure_response([
                'token_payload' => $payload,
                'header_decoded' => json_decode( base64_decode( $parts[0] ), true )
            ]);
        },
        'permission_callback' => '__return_true',
    ] );
} );

/**
 * Permission callback for SAP ShipTo API
 * Uses WordPress REST API authentication (Basic Auth or Application Passwords)
 */
function sap_shipto_permission_check( WP_REST_Request $request ) {
    error_log( "🔍 SAP ShipTo API: Checking WordPress REST API authentication" );

    // Get the current user (WordPress handles authentication automatically)
    $user = wp_get_current_user();

    if ( ! $user || ! $user->ID ) {
        error_log( "❌ SAP ShipTo API: No authenticated user found" );
        return new WP_Error( 'rest_not_logged_in', 'You are not currently logged in.', [ 'status' => 401 ] );
    }

    error_log( "🔍 SAP ShipTo API: Authenticated user: {$user->ID} ({$user->user_login})" );

    // Check if user has appropriate capabilities
    $required_capabilities = [
        'manage_options',       // Administrator capability
        'manage_woocommerce',   // WooCommerce admin
        'edit_shop_orders',     // WooCommerce orders
        'edit_users',           // User management
    ];

    foreach ( $required_capabilities as $cap ) {
        if ( user_can( $user, $cap ) ) {
            error_log( "✅ SAP ShipTo API: User has capability: {$cap}" );
            return true;
        }
    }

    // Check for specific roles
    $user_roles = $user->roles ?? [];
    $allowed_roles = [ 'administrator', 'shop_manager', 'editor' ];

    foreach ( $allowed_roles as $role ) {
        if ( in_array( $role, $user_roles ) ) {
            error_log( "✅ SAP ShipTo API: User has allowed role: {$role}" );
            return true;
        }
    }

    error_log( "❌ SAP ShipTo API: User lacks sufficient permissions. Roles: " . implode( ', ', $user_roles ) );
    return new WP_Error( 'rest_forbidden', 'Sorry, you are not allowed to access this endpoint.', [ 'status' => 403 ] );
}



/**
 * Main endpoint to process SAP shipping addresses
 */
function sap_process_shipto_addresses( WP_REST_Request $request ) {
    $start_time = microtime( true );
    $request_id = uniqid( 'sap_shipto_' );

    // Log API call start with comprehensive details
    sap_shipto_log_api( "🚀 SAP-ShipTo API CALL START [ID: {$request_id}]" );
    sap_shipto_log_api( "📡 Request Method: " . $request->get_method() );
    sap_shipto_log_api( "📡 Request URL: " . $request->get_route() );
    sap_shipto_log_api( "📡 Request Time: " . current_time( 'mysql' ) );
    sap_shipto_log_api( "📡 User Agent: " . ( $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown' ) );
    sap_shipto_log_api( "📡 Client IP: " . sap_shipto_get_client_ip() );

    // Log authentication details
    $current_user = wp_get_current_user();
    if ( $current_user && $current_user->ID ) {
        sap_shipto_log_api( "🔐 Authenticated User: {$current_user->ID} ({$current_user->user_login})" );
    }

    $data = $request->get_json_params();

    // Log the incoming data (sanitized for security)
    $sanitized_data = sap_shipto_sanitize_log_data( $data );
    sap_shipto_log_api( "� SAP-ShipTo Request Data [ID: {$request_id}]: " . wp_json_encode( $sanitized_data, JSON_PRETTY_PRINT ) );

    // Validation
    if ( empty( $data ) || ! is_array( $data ) || empty( $data['shipTo'] ) ) {
        error_log( "❌ Invalid data structure" );
        return new WP_Error( 'invalid_data', 'Invalid data structure. Expected shipTo array.', [ 'status' => 400 ] );
    }

    $ship_to_addresses = $data['shipTo'];
    if ( ! is_array( $ship_to_addresses ) ) {
        error_log( "❌ shipTo is not an array" );
        return new WP_Error( 'invalid_shipto', 'shipTo must be an array', [ 'status' => 400 ] );
    }

    // Step 1: Store all ship-to addresses in database first
    $shipto_by_customer = [];
    $errors = [];

    foreach ( $ship_to_addresses as $address ) {
        if ( empty( $address['customerId'] ) ) {
            $errors[] = "Address missing customerId: " . json_encode( $address );
            continue;
        }

        $customer_id = sanitize_text_field( $address['customerId'] );

        // Trim leading zeros from customer ID (consistent with other SAP APIs)
        $original_customer_id = $customer_id;
        $customer_id = ltrim( $customer_id, '0' );

        // If the customer ID becomes empty after trimming (was all zeros), keep one zero
        if ( empty( $customer_id ) ) {
            $customer_id = '0';
        }

        // Log the transformation if it occurred
        if ( $original_customer_id !== $customer_id ) {
            error_log( "🔄 SAP-ShipTo: Customer ID trimmed: '{$original_customer_id}' -> '{$customer_id}'" );
        }

        if ( ! isset( $shipto_by_customer[$customer_id] ) ) {
            $shipto_by_customer[$customer_id] = [];
        }
        $shipto_by_customer[$customer_id][] = $address;
    }

    error_log( "🔍 Grouped ship-to addresses for " . count( $shipto_by_customer ) . " customers" );

    // Step 2: Store ship-to addresses in database
    $processed_customers = [];
    foreach ( $shipto_by_customer as $customer_id => $addresses ) {
        error_log( "🔄 Processing " . count( $addresses ) . " ship-to addresses for customer: {$customer_id}" );

        // Convert all addresses to WCMCA format
        $wcmca_addresses = [];
        foreach ( $addresses as $address ) {
            $wcmca_address = convert_sap_to_wcmca_format( $address );
            if ( is_wp_error( $wcmca_address ) ) {
                $errors[] = "Address conversion error for customer {$customer_id}: " . $wcmca_address->get_error_message();
                continue 2; // Skip this customer entirely if any address fails
            }
            $wcmca_addresses[] = $wcmca_address;
        }

        // Update database table with serialized WCMCA data
        $db_result = sap_shipto_update_database( $customer_id, $wcmca_addresses, $addresses );
        if ( is_wp_error( $db_result ) ) {
            $errors[] = "Database error for customer {$customer_id}: " . $db_result->get_error_message();
            error_log( "❌ Database error for customer {$customer_id}: " . $db_result->get_error_message() );
            continue;
        }

        $processed_customers[] = [
            'customer_id' => $customer_id,
            'database_updated' => true,
            'database_addresses_count' => count( $wcmca_addresses ),
            'total_addresses' => count( $addresses )
        ];
    }

    // Step 3: Now find sold-to customers that reference the ship-to customers we just processed
    $processed_shipto_ids = array_keys( $shipto_by_customer );
    $soldto_updates = sap_shipto_update_soldto_users_with_shiptos( $processed_shipto_ids );

    // Return response
    $response = [
        'success' => true,
        'shipto_customers_processed' => $processed_customers,
        'shipto_total_processed' => count( $processed_customers ),
        'soldto_users_updated' => $soldto_updates,
        'errors' => $errors
    ];

    if ( ! empty( $errors ) ) {
        $response['partial_success'] = true;
    }

    // Log API call completion
    $end_time = microtime( true );
    $execution_time = round( ( $end_time - $start_time ) * 1000, 2 ); // Convert to milliseconds

    sap_shipto_log_api( "📤 SAP-ShipTo Response [ID: {$request_id}]: " . wp_json_encode( $response, JSON_PRETTY_PRINT ) );
    sap_shipto_log_api( "⏱️ SAP-ShipTo API CALL COMPLETE [ID: {$request_id}] - Execution Time: {$execution_time}ms" );
    sap_shipto_log_api( "✅ SAP-ShipTo Success: {$response['success']}, ShipTo Customers Processed: {$response['shipto_total_processed']}, SoldTo Users Updated: " . count( $response['soldto_users_updated'] ) );

    return rest_ensure_response( $response );
}

// Note: The old process_customer_shipto_addresses function has been removed
// as we now use a different approach where ship-to addresses are stored in database
// and then applied to sold-to users based on their shiptos array

/**
 * Find WordPress user by customerId
 */
function find_user_by_customer_id( $customer_id ) {
    $users = get_users([
        'meta_key' => '_customer',
        'meta_value' => $customer_id,
        'number' => 1,
        'fields' => 'ID'
    ]);

    return ! empty( $users ) ? $users[0] : false;
}

/**
 * Update sold-to users with their corresponding ship-to addresses
 * This function finds sold-to customers that reference the specified ship-to customer IDs
 * and updates their WordPress users with ship-to addresses based on the shiptos array
 *
 * @param array $processed_shipto_ids Array of ship-to customer IDs that were just processed
 */
function sap_shipto_update_soldto_users_with_shiptos( $processed_shipto_ids = [] ) {
    global $wpdb;

    sap_shipto_log_api( "🔄 Starting sold-to users update for ship-to IDs: " . implode( ', ', $processed_shipto_ids ) );

    $soldto_table = $wpdb->prefix . 'sap_soldto_customers';
    $shipto_table = $wpdb->prefix . 'sap_shipto_addresses';

    // Get sold-to customers that have shiptos data and reference the processed ship-to IDs
    if ( empty( $processed_shipto_ids ) ) {
        // If no specific IDs provided, process all (fallback behavior)
        $soldto_customers = $wpdb->get_results( "
            SELECT customer_id, shiptos, wp_user_id
            FROM {$soldto_table}
            WHERE shiptos IS NOT NULL
            AND shiptos != ''
            AND shiptos != '[]'
        " );
        sap_shipto_log_api( "🔍 No specific ship-to IDs provided, processing all sold-to customers" );
    } else {
        // Build a query to find sold-to customers whose shiptos array contains any of the processed IDs
        $like_conditions = [];
        foreach ( $processed_shipto_ids as $shipto_id ) {
            $like_conditions[] = $wpdb->prepare( "shiptos LIKE %s", '%"' . $wpdb->esc_like( $shipto_id ) . '"%' );
        }
        $like_clause = implode( ' OR ', $like_conditions );

        $soldto_customers = $wpdb->get_results( "
            SELECT customer_id, shiptos, wp_user_id
            FROM {$soldto_table}
            WHERE shiptos IS NOT NULL
            AND shiptos != ''
            AND shiptos != '[]'
            AND ({$like_clause})
        " );

        sap_shipto_log_api( "🔍 Found sold-to customers that reference the processed ship-to IDs" );
    }

    if ( empty( $soldto_customers ) ) {
        sap_shipto_log_api( "🔍 No sold-to customers found with shiptos data" );
        return [];
    }

    sap_shipto_log_api( "🔍 Found " . count( $soldto_customers ) . " sold-to customers with shiptos data" );

    $updated_users = [];

    foreach ( $soldto_customers as $soldto_customer ) {
        $soldto_customer_id = $soldto_customer->customer_id;
        $shiptos_json = $soldto_customer->shiptos;
        $wp_user_id = $soldto_customer->wp_user_id;

        // Parse the shiptos JSON array
        $shiptos = json_decode( $shiptos_json, true );
        if ( ! is_array( $shiptos ) || empty( $shiptos ) ) {
            sap_shipto_log_api( "⚠️ Invalid or empty shiptos data for sold-to customer {$soldto_customer_id}" );
            continue;
        }

        // If we have specific processed ship-to IDs, check if this sold-to customer references any of them
        if ( ! empty( $processed_shipto_ids ) ) {
            $has_relevant_shipto = false;
            foreach ( $processed_shipto_ids as $processed_id ) {
                if ( in_array( $processed_id, $shiptos ) ) {
                    $has_relevant_shipto = true;
                    break;
                }
            }

            if ( ! $has_relevant_shipto ) {
                sap_shipto_log_api( "⏭️ Skipping sold-to customer {$soldto_customer_id} - no relevant ship-to IDs found in shiptos: " . implode( ', ', $shiptos ) );
                continue;
            }
        }

        sap_shipto_log_api( "🔍 Processing sold-to customer {$soldto_customer_id} with " . count( $shiptos ) . " shiptos: " . implode( ', ', $shiptos ) );

        // Find WordPress user for this sold-to customer
        if ( ! $wp_user_id ) {
            // Try to find user by customer ID if not linked in database
            $wp_user_id = find_user_by_customer_id( $soldto_customer_id );
        }

        if ( ! $wp_user_id ) {
            sap_shipto_log_api( "⚠️ No WordPress user found for sold-to customer {$soldto_customer_id}" );
            continue;
        }

        // Collect all ship-to addresses for this sold-to customer
        $all_shipto_addresses = [];

        foreach ( $shiptos as $shipto_id ) {
            // Get ship-to addresses from database
            $shipto_data = sap_shipto_get_database_addresses( $shipto_id );

            if ( $shipto_data && ! empty( $shipto_data['addresses'] ) ) {
                sap_shipto_log_api( "✅ Found " . count( $shipto_data['addresses'] ) . " addresses for ship-to customer {$shipto_id}" );
                $all_shipto_addresses = array_merge( $all_shipto_addresses, $shipto_data['addresses'] );
            } else {
                sap_shipto_log_api( "⚠️ No ship-to addresses found for customer {$shipto_id}" );
            }
        }

        if ( empty( $all_shipto_addresses ) ) {
            sap_shipto_log_api( "⚠️ No ship-to addresses found for any shiptos of sold-to customer {$soldto_customer_id}" );
            continue;
        }

        // Update WordPress user with all collected ship-to addresses
        sap_shipto_log_api( "🔄 Attempting to update user {$wp_user_id} with " . count( $all_shipto_addresses ) . " addresses" );
        sap_shipto_log_api( "📊 Address data size: " . strlen( serialize( $all_shipto_addresses ) ) . " bytes" );

        // Check if addresses array is valid
        if ( empty( $all_shipto_addresses ) || ! is_array( $all_shipto_addresses ) ) {
            sap_shipto_log_api( "❌ Invalid addresses data for user {$wp_user_id}" );

            $updated_users[] = [
                'wp_user_id' => $wp_user_id,
                'soldto_customer_id' => $soldto_customer_id,
                'shiptos_processed' => $shiptos,
                'total_addresses' => count( $all_shipto_addresses ),
                'success' => false,
                'error' => 'Invalid addresses data'
            ];
            continue;
        }

        // Try to update user meta with error handling
        $result = update_user_meta( $wp_user_id, '_wcmca_additional_addresses', $all_shipto_addresses );

        // Get more detailed error information
        $last_error = error_get_last();

        if ( $result !== false ) {
            sap_shipto_log_api( "✅ Updated WordPress user {$wp_user_id} (sold-to: {$soldto_customer_id}) with " . count( $all_shipto_addresses ) . " ship-to addresses" );

            // Verify the update worked by reading it back
            $verification = get_user_meta( $wp_user_id, '_wcmca_additional_addresses', true );
            $verified_count = is_array( $verification ) ? count( $verification ) : 0;

            sap_shipto_log_api( "✅ Verification: User {$wp_user_id} now has {$verified_count} addresses stored" );

            $updated_users[] = [
                'wp_user_id' => $wp_user_id,
                'soldto_customer_id' => $soldto_customer_id,
                'shiptos_processed' => $shiptos,
                'total_addresses' => count( $all_shipto_addresses ),
                'verified_addresses' => $verified_count,
                'success' => true
            ];
        } else {
            $error_message = 'Failed to update user meta';
            if ( $last_error && isset( $last_error['message'] ) ) {
                $error_message .= ': ' . $last_error['message'];
            }

            sap_shipto_log_api( "❌ Failed to update WordPress user {$wp_user_id} for sold-to customer {$soldto_customer_id}: {$error_message}" );

            // Try to get current user meta to see if there's an existing issue
            $current_meta = get_user_meta( $wp_user_id, '_wcmca_additional_addresses', true );
            $current_count = is_array( $current_meta ) ? count( $current_meta ) : 0;

            sap_shipto_log_api( "🔍 Current user {$wp_user_id} has {$current_count} existing addresses" );

            $updated_users[] = [
                'wp_user_id' => $wp_user_id,
                'soldto_customer_id' => $soldto_customer_id,
                'shiptos_processed' => $shiptos,
                'total_addresses' => count( $all_shipto_addresses ),
                'current_addresses' => $current_count,
                'success' => false,
                'error' => $error_message
            ];
        }
    }

    sap_shipto_log_api( "✅ Completed sold-to users update. Processed " . count( $updated_users ) . " users" );

    return $updated_users;
}

/**
 * Convert SAP address format to WCMCA format
 */
function convert_sap_to_wcmca_format( $sap_address ) {
    // Generate unique address ID with zero-trimming
    if ( ! empty( $sap_address['addressID'] ) ) {
        $address_id = $sap_address['addressID'];

        // Trim leading zeros from address ID if it's numeric
        if ( is_numeric( $address_id ) ) {
            $original_address_id = $address_id;
            $address_id = ltrim( $address_id, '0' );

            // If becomes empty after trimming, keep one zero
            if ( empty( $address_id ) ) {
                $address_id = '0';
            }

            // Log the transformation if it occurred
            if ( $original_address_id !== $address_id ) {
                error_log( "🔄 SAP-ShipTo: Address ID trimmed: '{$original_address_id}' -> '{$address_id}'" );
            }
        }
    } else {
        $address_id = uniqid();
    }
    
    // Build WCMCA compatible address array
    $wcmca_address = [
        'type' => 'shipping',
        'address_id' => $address_id,
    ];

    // Map identifier to address_internal_name
    if ( ! empty( $sap_address['identifier'] ) ) {
        $wcmca_address['address_internal_name'] = sanitize_text_field( $sap_address['identifier'] );
    }

    // Map isDefaultAddress
    if ( isset( $sap_address['isDefaultAddress'] ) && $sap_address['isDefaultAddress'] === true ) {
        $wcmca_address['shipping_is_default_address'] = '1';
    }

    // Map company name
    if ( ! empty( $sap_address['companyName'] ) ) {
        $wcmca_address['shipping_company'] = sanitize_text_field( $sap_address['companyName'] );
    }

    // Map country
    if ( ! empty( $sap_address['country'] ) ) {
        $wcmca_address['shipping_country'] = sanitize_text_field( $sap_address['country'] );
    }

    // Map address fields
    if ( ! empty( $sap_address['address'] ) ) {
        $address = $sap_address['address'];
        
        // Combine street and house number for address_1
        $address_1_parts = [];
        if ( ! empty( $address['street'] ) ) {
            $address_1_parts[] = sanitize_text_field( $address['street'] );
        }
        if ( ! empty( $address['houseNumber'] ) ) {
            $address_1_parts[] = sanitize_text_field( $address['houseNumber'] );
        }
        if ( ! empty( $address_1_parts ) ) {
            $wcmca_address['shipping_address_1'] = implode( ' ', $address_1_parts );
        }

        // Map apartment to address_2
        if ( ! empty( $address['apartment'] ) ) {
            $wcmca_address['shipping_address_2'] = sanitize_text_field( $address['apartment'] );
        }

        // Map city
        if ( ! empty( $address['city'] ) ) {
            $wcmca_address['shipping_city'] = sanitize_text_field( $address['city'] );
        }

        // Map postal code
        if ( ! empty( $address['postalCode'] ) ) {
            $wcmca_address['shipping_postcode'] = sanitize_text_field( $address['postalCode'] );
        }
    }

    // Map stateCounty to shipping_state
    if ( ! empty( $sap_address['stateCounty'] ) ) {
        $wcmca_address['shipping_state'] = sanitize_text_field( $sap_address['stateCounty'] );
    }

    // Note: shipping_first_name and shipping_last_name are intentionally left empty
    // as per requirements (leave shipping_last_name empty)

    error_log( "🔍 Converted SAP address to WCMCA format: " . print_r( $wcmca_address, true ) );

    return $wcmca_address;
}

/**
 * Helper function to convert JSON to WordPress metadata format
 * Uses WordPress's built-in serialization for complex data
 */
function convert_json_to_metadata( $data ) {
    if ( is_array( $data ) || is_object( $data ) ) {
        return maybe_serialize( $data );
    }
    return $data;
}

/**
 * Update or create shipping addresses in the database table (serialized WCMCA format)
 */
function sap_shipto_update_database( $customer_id, $wcmca_addresses, $original_sap_data ) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sap_shipto_addresses';

    sap_shipto_log_api( "🗄️ Updating database table for customer {$customer_id} with " . count( $wcmca_addresses ) . " addresses" );

    try {
        // Serialize the WCMCA addresses data (same format as WordPress metadata)
        $serialized_wcmca = maybe_serialize( $wcmca_addresses );

        // Serialize the original SAP data for reference
        $serialized_sap = wp_json_encode( $original_sap_data );

        // Calculate summary information
        $total_addresses = count( $wcmca_addresses );
        $has_default = false;

        foreach ( $wcmca_addresses as $address ) {
            if ( ! empty( $address['shipping_is_default_address'] ) && $address['shipping_is_default_address'] === '1' ) {
                $has_default = true;
                break;
            }
        }

        // Prepare database record
        $db_record = [
            'customer_id' => $customer_id,
            'wcmca_addresses_data' => $serialized_wcmca,
            'sap_raw_data' => $serialized_sap,
            'total_addresses' => $total_addresses,
            'has_default_address' => $has_default ? 1 : 0,
            'status' => 'active',
            'updated_at' => current_time( 'mysql' )
        ];

        // Check if record exists for this customer
        $existing_record = $wpdb->get_row( $wpdb->prepare(
            "SELECT id, wp_user_id FROM {$table_name} WHERE customer_id = %s",
            $customer_id
        ) );

        if ( $existing_record ) {
            // Update existing record
            if ( $existing_record->wp_user_id ) {
                $db_record['wp_user_id'] = $existing_record->wp_user_id;
            }

            $result = $wpdb->update(
                $table_name,
                $db_record,
                [ 'customer_id' => $customer_id ],
                [ '%s', '%s', '%s', '%d', '%d', '%s', '%s' ],
                [ '%s' ]
            );

            if ( $result === false ) {
                throw new Exception( "Failed to update addresses for customer {$customer_id}: " . $wpdb->last_error );
            }

            sap_shipto_log_api( "🔄 Updated existing database record for customer {$customer_id} with {$total_addresses} addresses" );

        } else {
            // Insert new record
            $db_record['created_at'] = current_time( 'mysql' );

            $result = $wpdb->insert(
                $table_name,
                $db_record,
                [ '%s', '%s', '%s', '%d', '%d', '%s', '%s', '%s' ]
            );

            if ( $result === false ) {
                throw new Exception( "Failed to insert addresses for customer {$customer_id}: " . $wpdb->last_error );
            }

            sap_shipto_log_api( "➕ Inserted new database record for customer {$customer_id} with {$total_addresses} addresses" );
        }

        sap_shipto_log_api( "✅ Successfully stored {$total_addresses} addresses in database for customer {$customer_id}" );

        return [
            'success' => true,
            'addresses_count' => $total_addresses,
            'customer_id' => $customer_id,
            'has_default' => $has_default
        ];

    } catch ( Exception $e ) {
        sap_shipto_log_api( "❌ Database operation failed for customer {$customer_id}: " . $e->getMessage() );
        return new WP_Error( 'database_error', $e->getMessage() );
    }
}

/**
 * Get WCMCA addresses from database for a customer
 */
function sap_shipto_get_database_addresses( $customer_id ) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sap_shipto_addresses';

    $record = $wpdb->get_row( $wpdb->prepare(
        "SELECT wcmca_addresses_data, total_addresses, has_default_address, updated_at
         FROM {$table_name}
         WHERE customer_id = %s AND status = 'active'",
        $customer_id
    ) );

    if ( ! $record ) {
        return false;
    }

    $addresses = maybe_unserialize( $record->wcmca_addresses_data );

    return [
        'addresses' => $addresses,
        'total_addresses' => $record->total_addresses,
        'has_default_address' => (bool) $record->has_default_address,
        'updated_at' => $record->updated_at
    ];
}

/**
 * Link database record to WordPress user
 */
function sap_shipto_link_database_to_user( $customer_id, $user_id ) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'sap_shipto_addresses';

    $result = $wpdb->update(
        $table_name,
        [ 'wp_user_id' => $user_id ],
        [ 'customer_id' => $customer_id ],
        [ '%d' ],
        [ '%s' ]
    );

    if ( $result !== false ) {
        sap_shipto_log_api( "🔗 Linked database record for customer {$customer_id} to user {$user_id}" );
    } else {
        sap_shipto_log_api( "❌ Failed to link database record for customer {$customer_id} to user {$user_id}: " . $wpdb->last_error );
    }

    return $result !== false;
}

/**
 * Write to custom API log file
 */
function sap_shipto_log_api( $message ) {
    $log_dir = WP_CONTENT_DIR . '/logs';
    $log_file = $log_dir . '/sap_shipto.log';

    // Create logs directory if it doesn't exist
    if ( ! file_exists( $log_dir ) ) {
        wp_mkdir_p( $log_dir );
    }

    // Format log entry with timestamp
    $timestamp = current_time( 'Y-m-d H:i:s' );
    $log_entry = "[{$timestamp}] {$message}" . PHP_EOL;

    // Write to log file
    file_put_contents( $log_file, $log_entry, FILE_APPEND | LOCK_EX );

    // Also log to WordPress error log for backup (optional)
    error_log( $message );
}



/**
 * Get client IP address for logging
 */
function sap_shipto_get_client_ip() {
    $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

    foreach ( $ip_keys as $key ) {
        if ( ! empty( $_SERVER[$key] ) ) {
            $ip = sanitize_text_field( $_SERVER[$key] );
            // Handle comma-separated IPs (from proxies)
            if ( strpos( $ip, ',' ) !== false ) {
                $ip = trim( explode( ',', $ip )[0] );
            }
            if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) ) {
                return $ip;
            }
        }
    }

    return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
}

/**
 * Sanitize data for logging (remove sensitive information)
 */
function sap_shipto_sanitize_log_data( $data ) {
    if ( ! is_array( $data ) ) {
        return $data;
    }

    $sanitized = $data;

    // Remove or mask sensitive fields
    $sensitive_fields = ['password', 'token', 'secret', 'key'];

    array_walk_recursive( $sanitized, function( &$value, $key ) use ( $sensitive_fields ) {
        if ( in_array( strtolower( $key ), $sensitive_fields ) ) {
            $value = '[REDACTED]';
        }
        // Mask email addresses partially
        if ( strtolower( $key ) === 'email' && is_string( $value ) && strpos( $value, '@' ) !== false ) {
            $parts = explode( '@', $value );
            if ( count( $parts ) === 2 ) {
                $username = $parts[0];
                $domain = $parts[1];
                $masked_username = substr( $username, 0, 2 ) . str_repeat( '*', max( 0, strlen( $username ) - 2 ) );
                $value = $masked_username . '@' . $domain;
            }
        }
    });

    return $sanitized;
}
